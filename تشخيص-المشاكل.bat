@echo off
chcp 65001 >nul
title تشخيص مشاكل التطبيق

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔧 تشخيص مشاكل التطبيق                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص البيئة...
echo.

echo 📋 معلومات النظام:
echo OS: %OS%
echo PROCESSOR: %PROCESSOR_ARCHITECTURE%
echo.

echo 📋 إصدارات الأدوات:
echo Node.js:
node --version 2>nul || echo ❌ Node.js غير مثبت

echo npm:
npm --version 2>nul || echo ❌ npm غير متوفر

echo.
echo 📋 فحص الملفات المطلوبة:

if exist "package.json" (
    echo ✅ package.json موجود
) else (
    echo ❌ package.json مفقود
)

if exist "tsconfig.json" (
    echo ✅ tsconfig.json موجود
) else (
    echo ❌ tsconfig.json مفقود
)

if exist "vite.config.js" (
    echo ✅ vite.config.js موجود
) else (
    echo ❌ vite.config.js مفقود
)

if exist "src" (
    echo ✅ مجلد src موجود
) else (
    echo ❌ مجلد src مفقود
)

echo.
echo 📋 فحص التبعيات:

if exist "node_modules" (
    echo ✅ node_modules موجود
    echo 📊 عدد الحزم المثبتة:
    dir /b node_modules 2>nul | find /c /v "" || echo 0
) else (
    echo ❌ node_modules مفقود - يجب تشغيل npm install
)

echo.
echo 📋 فحص ملفات التكوين:

if exist ".npmrc" (
    echo ✅ .npmrc موجود
) else (
    echo ⚠️ .npmrc مفقود - سيتم استخدام الإعدادات الافتراضية
)

echo.
echo 🔧 اقتراحات الحلول:
echo.
echo 1. إذا كان Node.js مفقود: قم بتثبيته من nodejs.org
echo 2. إذا كانت التبعيات مفقودة: شغل npm install
echo 3. إذا كان التشغيل بطيء: استخدم تشغيل-سريع.bat
echo 4. إذا كانت هناك أخطاء: احذف node_modules وشغل npm install مرة أخرى
echo.

echo 📝 انتهى التشخيص
pause
