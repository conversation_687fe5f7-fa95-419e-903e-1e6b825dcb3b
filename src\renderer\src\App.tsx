import React, { useState, useEffect } from 'react'
import { Sidebar } from './components/Sidebar'
import { ChatInterface } from './components/ChatInterface'
import { SettingsPanel } from './components/SettingsPanel'
import { LMStudioSettings } from './components/LMStudioSettings'
import { LoadingScreen } from './components/LoadingScreen'
import { Conversation } from './types'
import './App.css'

interface AppState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  showSettings: boolean
  showLMStudioSettings: boolean
  isLoading: boolean
  settings: any
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    conversations: [],
    currentConversation: null,
    showSettings: false,
    showLMStudioSettings: false,
    isLoading: true,
    settings: { theme: 'dark', language: 'ar' }
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // تحميل الإعدادات
        const settingsResult = await window.electronAPI.getSettings()
        if (settingsResult.success) {
          setState(prev => ({ ...prev, settings: settingsResult.settings }))

          // تطبيق الثيم
          document.documentElement.setAttribute('data-theme', settingsResult.settings.theme || 'dark')
        }

        // تحميل المحادثات
        const conversationsResult = await window.electronAPI.getConversations()
        if (conversationsResult.success) {
          setState(prev => ({
            ...prev,
            conversations: conversationsResult.conversations || []
          }))
        }

        // فحص حالة الخدمات
        const servicesStatus = await window.electronAPI.getServicesStatus()
        console.log('حالة الخدمات:', servicesStatus)

      } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error)
      } finally {
        // إخفاء شاشة التحميل بعد 2 ثانية
        setTimeout(() => {
          setState(prev => ({ ...prev, isLoading: false }))
        }, 2000)
      }
    }

    initializeApp()
  }, [])

  // إنشاء محادثة جديدة
  const createNewConversation = async (title?: string) => {
    try {
      const result = await window.electronAPI.createConversation(
        title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`
      )

      if (result.success) {
        setState(prev => ({
          ...prev,
          conversations: [result.conversation, ...prev.conversations],
          currentConversation: result.conversation
        }))
      }
    } catch (error) {
      console.error('خطأ في إنشاء محادثة جديدة:', error)
      // إنشاء محادثة محلية في حالة فشل الاتصال
      const newConversation = {
        id: Date.now().toString(),
        title: title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`,
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      setState(prev => ({
        ...prev,
        conversations: [newConversation, ...prev.conversations],
        currentConversation: newConversation
      }))
    }
  }

  // تحديد المحادثة الحالية
  const selectConversation = async (conversationId: string) => {
    try {
      const result = await window.electronAPI.getConversation(conversationId)
      if (result.success) {
        setState(prev => ({ ...prev, currentConversation: result.conversation }))
      }
    } catch (error) {
      console.error('خطأ في تحميل المحادثة:', error)
      // البحث في المحادثات المحلية
      const conversation = state.conversations.find(c => c.id === conversationId)
      if (conversation) {
        setState(prev => ({ ...prev, currentConversation: conversation }))
      }
    }
  }

  // حذف محادثة
  const deleteConversation = async (conversationId: string) => {
    try {
      const result = await window.electronAPI.deleteConversation(conversationId)
      if (result.success) {
        setState(prev => ({
          ...prev,
          conversations: prev.conversations.filter(c => c.id !== conversationId),
          currentConversation: prev.currentConversation?.id === conversationId ? null : prev.currentConversation
        }))
      }
    } catch (error) {
      console.error('خطأ في حذف المحادثة:', error)
    }
  }

  // إرسال رسالة
  const sendMessage = async (message: string, model: string) => {
    if (!state.currentConversation) {
      await createNewConversation()
    }

    try {
      const result = await window.electronAPI.sendMessage({
        message,
        model,
        conversationId: state.currentConversation!.id
      })

      if (result.success) {
        setState(prev => ({
          ...prev,
          currentConversation: result.conversation,
          conversations: prev.conversations.map(c =>
            c.id === result.conversation.id ? result.conversation : c
          )
        }))
      }

      return result
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      return { success: false, error: error.message }
    }
  }

  // تحديث الإعدادات
  const updateSettings = async (newSettings: any) => {
    try {
      const result = await window.electronAPI.saveSettings(newSettings)
      if (result.success) {
        setState(prev => ({ ...prev, settings: { ...prev.settings, ...newSettings } }))

        // تطبيق الثيم الجديد
        if (newSettings.theme) {
          document.documentElement.setAttribute('data-theme', newSettings.theme)
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث الإعدادات:', error)
    }
  }

  // عرض شاشة التحميل
  if (state.isLoading) {
    return <LoadingScreen />
  }

  return (
    <div className="app">
      <div className="app-container">
        {/* الشريط الجانبي */}
        <Sidebar
          conversations={state.conversations}
          currentConversation={state.currentConversation}
          onSelectConversation={selectConversation}
          onNewConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onShowSettings={() => setState(prev => ({ ...prev, showSettings: true }))}
          onShowLMStudioSettings={() => setState(prev => ({ ...prev, showLMStudioSettings: true }))}
        />

        {/* المحتوى الرئيسي */}
        <div className="main-content">
          <ChatInterface
            conversation={state.currentConversation}
            onSendMessage={sendMessage}
            onNewConversation={createNewConversation}
          />
        </div>
      </div>

      {/* النوافذ المنبثقة */}
      {state.showSettings && (
        <SettingsPanel
          settings={state.settings}
          onSave={updateSettings}
          onClose={() => setState(prev => ({ ...prev, showSettings: false }))}
        />
      )}

      {state.showLMStudioSettings && (
        <LMStudioSettings
          onClose={() => setState(prev => ({ ...prev, showLMStudioSettings: false }))}
        />
      )}
    </div>
  )
}

export default App