import React, { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import Sidebar from './components/Sidebar'
import SettingsPanel from './components/SettingsPanel'
import LoadingScreen from './components/LoadingScreen'
import GeniusTraining from './components/GeniusTraining'
import { Conversation } from './types'
import './App.css'

interface AppState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  isLoading: boolean
  showSettings: boolean
  showTraining: boolean
  settings: any
  availableModels: any[]
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    conversations: [],
    currentConversation: null,
    isLoading: true,
    showSettings: false,
    showTraining: false,
    settings: {},
    availableModels: []
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }))

      // تحميل الإعدادات
      const settingsResult = await window.electronAPI.getSettings()
      if (settingsResult.success) {
        setState(prev => ({ ...prev, settings: settingsResult.settings }))

        // تطبيق السمة
        document.documentElement.setAttribute('data-theme', settingsResult.settings.theme || 'light')
      }

      // تحميل النماذج المتاحة
      const modelsResult = await window.electronAPI.getAvailableModels()
      if (modelsResult.success) {
        setState(prev => ({ ...prev, availableModels: modelsResult.models }))
      }

      // تحميل المحادثات
      const conversationsResult = await window.electronAPI.getConversations()
      if (conversationsResult.success) {
        setState(prev => ({
          ...prev,
          conversations: conversationsResult.conversations,
          currentConversation: conversationsResult.conversations[0] || null
        }))
      }

    } catch (error) {
      console.error('خطأ في تهيئة التطبيق:', error)
    } finally {
      setState(prev => ({ ...prev, isLoading: false }))
    }
  }

  // إنشاء محادثة جديدة
  const createNewConversation = async (title?: string) => {
    try {
      const result = await window.electronAPI.createConversation(
        title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`
      )

      if (result.success) {
        setState(prev => ({
          ...prev,
          conversations: [result.conversation, ...prev.conversations],
          currentConversation: result.conversation
        }))
      }
    } catch (error) {
      console.error('خطأ في إنشاء محادثة جديدة:', error)
    }
  }

  // تحديد المحادثة الحالية
  const selectConversation = async (conversationId: string) => {
    try {
      const result = await window.electronAPI.getConversation(conversationId)
      if (result.success) {
        setState(prev => ({ ...prev, currentConversation: result.conversation }))
      }
    } catch (error) {
      console.error('خطأ في تحميل المحادثة:', error)
    }
  }

  // حذف محادثة
  const deleteConversation = async (conversationId: string) => {
    try {
      const result = await window.electronAPI.deleteConversation(conversationId)
      if (result.success) {
        setState(prev => {
          const updatedConversations = prev.conversations.filter(c => c.id !== conversationId)
          const newCurrent = prev.currentConversation?.id === conversationId
            ? updatedConversations[0] || null
            : prev.currentConversation

          return {
            ...prev,
            conversations: updatedConversations,
            currentConversation: newCurrent
          }
        })
      }
    } catch (error) {
      console.error('خطأ في حذف المحادثة:', error)
    }
  }

  // إرسال رسالة
  const sendMessage = async (message: string, model: string) => {
    if (!state.currentConversation) {
      await createNewConversation()
    }

    try {
      const result = await window.electronAPI.sendMessage({
        message,
        model,
        conversationId: state.currentConversation!.id
      })

      if (result.success) {
        setState(prev => ({
          ...prev,
          currentConversation: result.conversation,
          conversations: prev.conversations.map(c =>
            c.id === result.conversation.id ? result.conversation : c
          )
        }))
      }

      return result
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      return { success: false, error: error.message }
    }
  }

  // حفظ الإعدادات
  const saveSettings = async (newSettings: any) => {
    try {
      const result = await window.electronAPI.saveSettings(newSettings)
      if (result.success) {
        setState(prev => ({ ...prev, settings: { ...prev.settings, ...newSettings } }))

        // تطبيق السمة الجديدة
        if (newSettings.theme) {
          document.documentElement.setAttribute('data-theme', newSettings.theme)
        }
      }
      return result
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error)
      return { success: false, error: error.message }
    }
  }

  // تصدير محادثة
  const exportConversation = async (conversationId: string, format: string) => {
    try {
      const result = await window.electronAPI.exportConversation({ conversationId, format })
      return result
    } catch (error) {
      console.error('خطأ في تصدير المحادثة:', error)
      return { success: false, error: error.message }
    }
  }

  if (state.isLoading) {
    return <LoadingScreen />
  }

  return (
    <div className="app">
      <div className="app-container">
        {/* الشريط الجانبي */}
        <Sidebar
          conversations={state.conversations}
          currentConversation={state.currentConversation}
          onSelectConversation={selectConversation}
          onCreateConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onExportConversation={exportConversation}
          onShowSettings={() => setState(prev => ({ ...prev, showSettings: true }))}
          onShowTraining={() => setState(prev => ({ ...prev, showTraining: true }))}
        />

        {/* واجهة الدردشة الرئيسية */}
        <div className="main-content">
          {state.showTraining ? (
            <GeniusTraining />
          ) : (
            <ChatInterface
              conversation={state.currentConversation}
              availableModels={state.availableModels}
              settings={state.settings}
              onSendMessage={sendMessage}
              onCreateConversation={createNewConversation}
            />
          )}
        </div>

        {/* لوحة الإعدادات */}
        {state.showSettings && (
          <SettingsPanel
            settings={state.settings}
            availableModels={state.availableModels}
            onSaveSettings={saveSettings}
            onClose={() => setState(prev => ({ ...prev, showSettings: false }))}
          />
        )}
      </div>
    </div>
  )
}

export default App
