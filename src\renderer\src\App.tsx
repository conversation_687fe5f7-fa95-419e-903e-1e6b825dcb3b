import React, { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import Sidebar from './components/Sidebar'
import SettingsPanel from './components/SettingsPanel'
import LoadingScreen from './components/LoadingScreen'
import { Conversation } from './types'
import './App.css'

interface AppState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  isLoading: boolean
  showSettings: boolean
  settings: any
  availableModels: any[]
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    conversations: [],
    currentConversation: null,
    isLoading: true,
    showSettings: false,
    settings: {},
    availableModels: []
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    console.log('🚀 بدء تهيئة التطبيق...')

    try {
      // تحميل الإعدادات (مع معالجة الأخطاء)
      try {
        if (window.electronAPI?.getSettings) {
          const settingsResult = await window.electronAPI.getSettings()
          if (settingsResult?.success) {
            setState(prev => ({ ...prev, settings: settingsResult.settings }))
            document.documentElement.setAttribute('data-theme', settingsResult.settings.theme || 'dark')
            console.log('✅ تم تحميل الإعدادات')
          }
        }
      } catch (error) {
        console.log('⚠️ لم يتم تحميل الإعدادات، استخدام الافتراضية')
      }

      // تحميل النماذج المتاحة (اختياري)
      try {
        if (window.electronAPI?.getAvailableModels) {
          const modelsResult = await window.electronAPI.getAvailableModels()
          if (modelsResult?.success) {
            setState(prev => ({ ...prev, availableModels: modelsResult.models || [] }))
            console.log('✅ تم تحميل النماذج')
          }
        }
      } catch (error) {
        console.log('⚠️ لم يتم تحميل النماذج')
      }

      // تحميل المحادثات (اختياري)
      try {
        if (window.electronAPI?.getConversations) {
          const conversationsResult = await window.electronAPI.getConversations()
          if (conversationsResult?.success) {
            setState(prev => ({
              ...prev,
              conversations: conversationsResult.conversations || [],
              currentConversation: conversationsResult.conversations?.[0] || null
            }))
            console.log('✅ تم تحميل المحادثات')
          }
        }
      } catch (error) {
        console.log('⚠️ لم يتم تحميل المحادثات، بدء بقائمة فارغة')
      }

      console.log('✅ تم إنجاز التهيئة بنجاح')

    } catch (error) {
      console.error('❌ خطأ في تهيئة التطبيق:', error)
    } finally {
      // إخفاء شاشة التحميل فوراً
      console.log('🎯 إخفاء شاشة التحميل...')
      setState(prev => ({ ...prev, isLoading: false }))
    }
  }

  // إنشاء محادثة جديدة
  const createNewConversation = async (title?: string) => {
    try {
      const result = await window.electronAPI.createConversation(
        title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`
      )

      if (result.success) {
        setState(prev => ({
          ...prev,
          conversations: [result.conversation, ...prev.conversations],
          currentConversation: result.conversation
        }))
      }
    } catch (error) {
      console.error('خطأ في إنشاء محادثة جديدة:', error)
    }
  }

  // تحديد المحادثة الحالية
  const selectConversation = async (conversationId: string) => {
    try {
      const result = await window.electronAPI.getConversation(conversationId)
      if (result.success) {
        setState(prev => ({ ...prev, currentConversation: result.conversation }))
      }
    } catch (error) {
      console.error('خطأ في تحميل المحادثة:', error)
    }
  }

  // حذف محادثة
  const deleteConversation = async (conversationId: string) => {
    try {
      const result = await window.electronAPI.deleteConversation(conversationId)
      if (result.success) {
        setState(prev => {
          const updatedConversations = prev.conversations.filter(c => c.id !== conversationId)
          const newCurrent = prev.currentConversation?.id === conversationId
            ? updatedConversations[0] || null
            : prev.currentConversation

          return {
            ...prev,
            conversations: updatedConversations,
            currentConversation: newCurrent
          }
        })
      }
    } catch (error) {
      console.error('خطأ في حذف المحادثة:', error)
    }
  }

  // إرسال رسالة
  const sendMessage = async (message: string, model: string) => {
    if (!state.currentConversation) {
      await createNewConversation()
    }

    try {
      const result = await window.electronAPI.sendMessage({
        message,
        model,
        conversationId: state.currentConversation!.id
      })

      if (result.success) {
        setState(prev => ({
          ...prev,
          currentConversation: result.conversation,
          conversations: prev.conversations.map(c =>
            c.id === result.conversation.id ? result.conversation : c
          )
        }))
      }

      return result
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      return { success: false, error: error.message }
    }
  }

  // حفظ الإعدادات
  const saveSettings = async (newSettings: any) => {
    try {
      const result = await window.electronAPI.saveSettings(newSettings)
      if (result.success) {
        setState(prev => ({ ...prev, settings: { ...prev.settings, ...newSettings } }))

        // تطبيق السمة الجديدة
        if (newSettings.theme) {
          document.documentElement.setAttribute('data-theme', newSettings.theme)
        }
      }
      return result
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error)
      return { success: false, error: error.message }
    }
  }

  // تحديث الإعدادات
  const updateSettings = async (newSettings: any) => {
    try {
      const result = await window.electronAPI?.saveSettings?.(newSettings)
      if (result?.success) {
        setState(prev => ({ ...prev, settings: { ...prev.settings, ...newSettings } }))

        // تطبيق الثيم الجديد
        if (newSettings.theme) {
          document.documentElement.setAttribute('data-theme', newSettings.theme)
        }

        return { success: true }
      }
      return result || { success: false, error: 'فشل في الحفظ' }
    } catch (error) {
      console.error('خطأ في تحديث الإعدادات:', error)
      return { success: false, error: error.message }
    }
  }

  // تصدير محادثة
  const exportConversation = async (conversationId: string, format: string) => {
    try {
      const result = await window.electronAPI.exportConversation({ conversationId, format })
      return result
    } catch (error) {
      console.error('خطأ في تصدير المحادثة:', error)
      return { success: false, error: error.message }
    }
  }

  if (state.isLoading) {
    return <LoadingScreen />
  }

  return (
    <div className="app">
      <div className="app-container">
        {/* الشريط الجانبي */}
        <Sidebar
          conversations={state.conversations}
          currentConversation={state.currentConversation}
          onSelectConversation={selectConversation}
          onCreateConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onExportConversation={exportConversation}
          onShowSettings={() => setState(prev => ({ ...prev, showSettings: true }))}
        />

        {/* واجهة الدردشة الرئيسية */}
        <div className="main-content">
          <ChatInterface
            conversation={state.currentConversation}
            availableModels={state.availableModels || []}
            settings={state.settings}
            onSendMessage={sendMessage}
            onCreateConversation={createNewConversation}
          />
        </div>

        {/* لوحة الإعدادات */}
        {state.showSettings && (
          <SettingsPanel
            settings={state.settings}
            availableModels={state.availableModels || []}
            onSaveSettings={updateSettings}
            onClose={() => setState(prev => ({ ...prev, showSettings: false }))}
          />
        )}
      </div>
    </div>
  )
}

export default App
