@echo off
chcp 65001 >nul
title تشغيل فوري للتطبيق

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔥 تشغيل فوري للتطبيق                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 تشغيل مباشر بدون TypeScript...
echo.

:: تشغيل Electron مباشرة
echo 🚀 تشغيل Electron...

if exist "node_modules\.bin\electron.cmd" (
    echo ✅ وجدت Electron في .bin
    echo 🔄 تشغيل التطبيق...
    node_modules\.bin\electron.cmd .
    goto :success
)

if exist "node_modules\electron\dist\electron.exe" (
    echo ✅ وجدت Electron في dist
    echo 🔄 تشغيل التطبيق...
    node_modules\electron\dist\electron.exe .
    goto :success
)

:: البحث في مجلدات فرعية
for /d %%i in (node_modules\*electron*) do (
    if exist "%%i\dist\electron.exe" (
        echo ✅ وجدت Electron في %%i
        echo 🔄 تشغيل التطبيق...
        "%%i\dist\electron.exe" .
        goto :success
    )
)

:: تجربة npm start
echo 🔄 محاولة npm start...
npm start
if not errorlevel 1 goto :success

:: تجربة electron عالمي
echo 🔄 محاولة electron عالمي...
electron . 2>nul
if not errorlevel 1 goto :success

echo ❌ لم أجد Electron
echo 💡 جرب: npm install electron
goto :end

:success
echo ✅ تم تشغيل التطبيق بنجاح!

:end
echo.
echo 📝 انتهى التشغيل
pause
