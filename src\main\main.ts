import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron'
import { join } from 'path'
import Store from 'electron-store'
import { OpenRouterAPI } from '../services/OpenRouterAPI'
import { ChatManager } from '../services/ChatManager'
import { FileExporter } from '../utils/FileExporter'
import { unifiedAI } from '../services/UnifiedAIService'

// إعداد التخزين المحلي
const store = new Store()

// متغيرات عامة
let mainWindow: BrowserWindow | null = null
let chatManager: ChatManager
let openRouterAPI: OpenRouterAPI

const isDev = process.env.NODE_ENV === 'development'

function createWindow(): void {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, '../preload/preload.js'),
    },
    icon: join(__dirname, '../../assets/icon.png'),
    titleBarStyle: 'default',
    show: false,
  })

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // إظهار النافذة عند الاستعداد
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
  })

  // إغلاق التطبيق عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // منع التنقل إلى روابط خارجية
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// تهيئة الخدمات
function initializeServices(): void {
  const apiKey = store.get('openrouter_api_key', '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') as string
  openRouterAPI = new OpenRouterAPI(apiKey)
  chatManager = new ChatManager(store)

  // تهيئة الخدمة الموحدة
  unifiedAI.updateConfig({
    openRouterApiKey: apiKey,
    preferredProvider: store.get('preferred_provider', 'lmstudio') as any,
    fallbackToOpenRouter: store.get('fallback_to_openrouter', true) as boolean
  })
}

// معالجات IPC
function setupIpcHandlers(): void {
  // إرسال رسالة للذكاء الاصطناعي (الطريقة الجديدة)
  ipcMain.handle('send-message', async (event, { message, model, conversationId }) => {
    try {
      const result = await chatManager.sendMessage(conversationId, message)
      return {
        success: true,
        response: result.response,
        conversation: result.conversation,
        usedProvider: result.usedProvider,
        modelUsed: result.modelUsed
      }
    } catch (error) {
      console.error('Error sending message:', error)
      return { success: false, error: error.message }
    }
  })

  // الحصول على قائمة النماذج المتاحة (الطريقة الجديدة)
  ipcMain.handle('get-available-models', async () => {
    try {
      const models = await chatManager.getAvailableModels()
      return { success: true, models }
    } catch (error) {
      console.error('Error getting models:', error)
      return { success: false, error: error.message }
    }
  })

  // إنشاء محادثة جديدة
  ipcMain.handle('create-conversation', async (event, title) => {
    try {
      const conversation = await chatManager.createConversation(title)
      return { success: true, conversation }
    } catch (error) {
      console.error('Error creating conversation:', error)
      return { success: false, error: error.message }
    }
  })

  // الحصول على جميع المحادثات
  ipcMain.handle('get-conversations', async () => {
    try {
      const conversations = await chatManager.getConversations()
      return { success: true, conversations }
    } catch (error) {
      console.error('Error getting conversations:', error)
      return { success: false, error: error.message }
    }
  })

  // الحصول على محادثة محددة
  ipcMain.handle('get-conversation', async (event, conversationId) => {
    try {
      const conversation = await chatManager.getConversation(conversationId)
      return { success: true, conversation }
    } catch (error) {
      console.error('Error getting conversation:', error)
      return { success: false, error: error.message }
    }
  })

  // حذف محادثة
  ipcMain.handle('delete-conversation', async (event, conversationId) => {
    try {
      await chatManager.deleteConversation(conversationId)
      return { success: true }
    } catch (error) {
      console.error('Error deleting conversation:', error)
      return { success: false, error: error.message }
    }
  })

  // تصدير المحادثة
  ipcMain.handle('export-conversation', async (event, { conversationId, format }) => {
    try {
      const conversation = await chatManager.getConversation(conversationId)
      if (!conversation) {
        throw new Error('Conversation not found')
      }

      const result = await dialog.showSaveDialog(mainWindow!, {
        defaultPath: `${conversation.title}.${format}`,
        filters: [
          { name: format.toUpperCase(), extensions: [format] }
        ]
      })

      if (!result.canceled && result.filePath) {
        const exporter = new FileExporter()
        await exporter.exportConversation(conversation, result.filePath, format)
        return { success: true, filePath: result.filePath }
      }

      return { success: false, error: 'Export cancelled' }
    } catch (error) {
      console.error('Error exporting conversation:', error)
      return { success: false, error: error.message }
    }
  })

  // حفظ الإعدادات
  ipcMain.handle('save-settings', async (event, settings) => {
    try {
      Object.keys(settings).forEach(key => {
        store.set(key, settings[key])
      })

      // تحديث مفتاح API إذا تم تغييره
      if (settings.openrouter_api_key) {
        openRouterAPI.updateApiKey(settings.openrouter_api_key)
      }

      return { success: true }
    } catch (error) {
      console.error('Error saving settings:', error)
      return { success: false, error: error.message }
    }
  })

  // الحصول على الإعدادات
  ipcMain.handle('get-settings', async () => {
    try {
      const settings = {
        openrouter_api_key: store.get('openrouter_api_key', ''),
        default_model: store.get('default_model', 'meta-llama/llama-3.3-8b-instruct:free'),
        theme: store.get('theme', 'dark'),
        language: store.get('language', 'ar'),
        auto_save: store.get('auto_save', true),
      }
      return { success: true, settings }
    } catch (error) {
      console.error('Error getting settings:', error)
      return { success: false, error: error.message }
    }
  })

  // معالجات الخدمة الموحدة الجديدة

  // تحديث إعدادات الذكاء الاصطناعي
  ipcMain.handle('update-ai-settings', async (event, settings) => {
    try {
      await chatManager.updateAISettings(settings)
      return { success: true }
    } catch (error) {
      console.error('Error updating AI settings:', error)
      return { success: false, error: error.message }
    }
  })

  // الحصول على حالة الخدمات
  ipcMain.handle('get-services-status', async () => {
    try {
      const status = await chatManager.getServicesStatus()
      return { success: true, status }
    } catch (error) {
      console.error('Error getting services status:', error)
      return { success: false, error: error.message }
    }
  })

  // تشغيل LM Studio
  ipcMain.handle('launch-lm-studio', async () => {
    try {
      const success = await chatManager.launchLMStudio()
      return { success }
    } catch (error) {
      console.error('Error launching LM Studio:', error)
      return { success: false, error: error.message }
    }
  })
}

// تهيئة التطبيق
app.whenReady().then(() => {
  initializeServices()
  setupIpcHandlers()
  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// إغلاق التطبيق على جميع المنصات عدا macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

// الأمان: منع إنشاء نوافذ جديدة
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    shell.openExternal(navigationUrl)
  })
})
