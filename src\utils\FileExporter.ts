import { writeFile } from 'fs/promises'
// import jsPDF from 'jspdf'
import { Conversation } from '../services/ChatManager'

export class FileExporter {

  // تصدير المحادثة كملف نصي
  async exportAsText(conversation: any, filePath: string): Promise<void> {
    let content = `عنوان المحادثة: ${conversation.title}\n`
    content += `تاريخ الإنشاء: ${new Date(conversation.createdAt).toLocaleString('ar-SA')}\n`
    content += `النموذج المستخدم: ${conversation.model}\n`
    content += `عدد الرسائل: ${conversation.messages.length}\n`
    content += '\n' + '='.repeat(50) + '\n\n'

    conversation.messages.forEach((message: any, index: number) => {
      const timestamp = new Date(message.timestamp).toLocaleString('ar-SA')
      const role = message.role === 'user' ? 'المستخدم' : 'المساعد'

      content += `[${index + 1}] ${role} - ${timestamp}\n`
      content += `${message.content}\n\n`
      content += '-'.repeat(30) + '\n\n'
    })

    content += `\nتم التصدير في: ${new Date().toLocaleString('ar-SA')}\n`
    content += 'تم إنشاؤه بواسطة: AI Chat Bot'

    await writeFile(filePath, content, 'utf8')
  }

  // تصدير المحادثة كملف PDF (مبسط)
  async exportAsPDF(conversation: any, filePath: string): Promise<void> {
    // تحويل إلى HTML ثم PDF (مبسط)
    let htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl">
    <head>
      <meta charset="UTF-8">
      <title>${conversation.title}</title>
      <style>
        body { font-family: Arial, sans-serif; direction: rtl; }
        .message { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .user { background: #e3f2fd; }
        .assistant { background: #f3e5f5; }
      </style>
    </head>
    <body>
      <h1>${conversation.title}</h1>
      <p>تاريخ الإنشاء: ${new Date(conversation.createdAt).toLocaleString('ar-SA')}</p>
      <p>النموذج: ${conversation.model}</p>
      <hr>
    `

    conversation.messages.forEach((message: any, index: number) => {
      const role = message.role === 'user' ? 'المستخدم' : 'المساعد'
      const className = message.role === 'user' ? 'user' : 'assistant'
      htmlContent += `
        <div class="message ${className}">
          <h4>[${index + 1}] ${role} - ${new Date(message.timestamp).toLocaleString('ar-SA')}</h4>
          <p>${message.content.replace(/\n/g, '<br>')}</p>
        </div>
      `
    })

    htmlContent += `
      <hr>
      <p><small>تم التصدير في: ${new Date().toLocaleString('ar-SA')}</small></p>
    </body>
    </html>
    `

    // حفظ كـ HTML بدلاً من PDF مؤقتاً
    const htmlPath = filePath.replace('.pdf', '.html')
    await writeFile(htmlPath, htmlContent, 'utf8')

    console.log('تم حفظ الملف كـ HTML:', htmlPath)
  }

  // تصدير المحادثة كملف JSON
  async exportAsJSON(conversation: any, filePath: string): Promise<void> {
    const exportData = {
      metadata: {
        title: conversation.title,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
        model: conversation.model,
        messageCount: conversation.messages.length,
        exportedAt: new Date().toISOString(),
        exportedBy: 'AI Chat Bot v1.0'
      },
      conversation: {
        id: conversation.id,
        title: conversation.title,
        messages: conversation.messages.map((msg: any) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp,
          model: msg.model
        }))
      }
    }

    await writeFile(filePath, JSON.stringify(exportData, null, 2), 'utf8')
  }

  // تصدير المحادثة كملف HTML
  async exportAsHTML(conversation: any, filePath: string): Promise<void> {
    const html = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${conversation.title}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .metadata {
            color: #666;
            font-size: 14px;
        }
        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .message.user {
            background-color: #e3f2fd;
            border-left-color: #2196f3;
        }
        .message.assistant {
            background-color: #f3e5f5;
            border-left-color: #9c27b0;
        }
        .message-header {
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .message-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .timestamp {
            font-size: 12px;
            color: #888;
            margin-top: 8px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">${conversation.title}</div>
            <div class="metadata">
                <div>تاريخ الإنشاء: ${new Date(conversation.createdAt).toLocaleString('ar-SA')}</div>
                <div>النموذج المستخدم: ${conversation.model}</div>
                <div>عدد الرسائل: ${conversation.messages.length}</div>
            </div>
        </div>

        <div class="messages">
            ${conversation.messages.map((message: any, index: number) => `
                <div class="message ${message.role}">
                    <div class="message-header">
                        [${index + 1}] ${message.role === 'user' ? 'المستخدم' : 'المساعد'}
                    </div>
                    <div class="message-content">${this.escapeHtml(message.content)}</div>
                    <div class="timestamp">${new Date(message.timestamp).toLocaleString('ar-SA')}</div>
                </div>
            `).join('')}
        </div>

        <div class="footer">
            <div>تم التصدير في: ${new Date().toLocaleString('ar-SA')}</div>
            <div>تم إنشاؤه بواسطة: AI Chat Bot</div>
        </div>
    </div>
</body>
</html>`

    await writeFile(filePath, html, 'utf8')
  }

  // تصدير المحادثة حسب التنسيق المطلوب
  async exportConversation(conversation: any, filePath: string, format: string): Promise<void> {
    switch (format.toLowerCase()) {
      case 'txt':
        await this.exportAsText(conversation, filePath)
        break
      case 'pdf':
        await this.exportAsPDF(conversation, filePath)
        break
      case 'json':
        await this.exportAsJSON(conversation, filePath)
        break
      case 'html':
        await this.exportAsHTML(conversation, filePath)
        break
      default:
        throw new Error(`تنسيق غير مدعوم: ${format}`)
    }
  }

  // تنظيف HTML
  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  // تصدير جميع المحادثات
  async exportAllConversations(conversations: any[], filePath: string, format: string): Promise<void> {
    if (format.toLowerCase() === 'json') {
      const exportData = {
        metadata: {
          totalConversations: conversations.length,
          exportedAt: new Date().toISOString(),
          exportedBy: 'AI Chat Bot v1.0'
        },
        conversations: conversations
      }

      await writeFile(filePath, JSON.stringify(exportData, null, 2), 'utf8')
    } else {
      throw new Error('تصدير جميع المحادثات متاح فقط بتنسيق JSON')
    }
  }
}
