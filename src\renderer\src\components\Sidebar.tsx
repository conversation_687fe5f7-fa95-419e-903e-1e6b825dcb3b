import React, { useState } from 'react'
import {
  Plus,
  MessageSquare,
  Settings,
  Search,
  MoreVertical,
  Trash2,
  Download,
  Edit3,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>O<PERSON>
} from 'lucide-react'
import { Conversation } from '../types'
import './Sidebar.css'

interface SidebarProps {
  conversations: Conversation[]
  currentConversation: Conversation | null
  onSelectConversation: (conversationId: string) => void
  onCreateConversation: () => void
  onDeleteConversation: (conversationId: string) => void
  onExportConversation: (conversationId: string, format: string) => Promise<any>
  onShowSettings: () => void
  onShowTraining: () => void
  onShowKnowledge: () => void
}

const Sidebar: React.FC<SidebarProps> = ({
  conversations,
  currentConversation,
  onSelectConversation,
  onCreateConversation,
  onDeleteConversation,
  onExportConversation,
  onShowSettings,
  onShowTraining,
  onShowKnowledge
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeMenu, setActiveMenu] = useState<string | null>(null)
  const [isCollapsed, setIsCollapsed] = useState(false)

  // فلترة المحادثات حسب البحث
  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.messages.some(msg =>
      msg.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
  )

  // تجميع المحادثات حسب التاريخ
  const groupConversationsByDate = (conversations: Conversation[]) => {
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    const lastWeek = new Date(today)
    lastWeek.setDate(lastWeek.getDate() - 7)

    const groups = {
      today: [] as Conversation[],
      yesterday: [] as Conversation[],
      thisWeek: [] as Conversation[],
      older: [] as Conversation[]
    }

    conversations.forEach(conv => {
      const convDate = new Date(conv.updatedAt)

      if (convDate.toDateString() === today.toDateString()) {
        groups.today.push(conv)
      } else if (convDate.toDateString() === yesterday.toDateString()) {
        groups.yesterday.push(conv)
      } else if (convDate >= lastWeek) {
        groups.thisWeek.push(conv)
      } else {
        groups.older.push(conv)
      }
    })

    return groups
  }

  const conversationGroups = groupConversationsByDate(filteredConversations)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleMenuClick = (conversationId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setActiveMenu(activeMenu === conversationId ? null : conversationId)
  }

  const handleExport = async (conversationId: string, format: string) => {
    try {
      const result = await onExportConversation(conversationId, format)
      if (result.success) {
        // إشعار نجح التصدير
        console.log('تم تصدير المحادثة بنجاح')
      }
    } catch (error) {
      console.error('خطأ في تصدير المحادثة:', error)
    }
    setActiveMenu(null)
  }

  const handleDelete = (conversationId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المحادثة؟')) {
      onDeleteConversation(conversationId)
    }
    setActiveMenu(null)
  }

  const renderConversationGroup = (title: string, conversations: Conversation[]) => {
    if (conversations.length === 0) return null

    return (
      <div className="conversation-group" key={title}>
        <div className="group-header">
          <span className="group-title">{title}</span>
          <span className="group-count">{conversations.length}</span>
        </div>

        <div className="conversations-list">
          {conversations.map(conversation => (
            <div
              key={conversation.id}
              className={`conversation-item ${
                currentConversation?.id === conversation.id ? 'active' : ''
              }`}
              onClick={() => onSelectConversation(conversation.id)}
            >
              <div className="conversation-content">
                <div className="conversation-header">
                  <h4 className="conversation-title">{conversation.title}</h4>
                  <button
                    className="conversation-menu-btn"
                    onClick={(e) => handleMenuClick(conversation.id, e)}
                  >
                    <MoreVertical size={16} />
                  </button>
                </div>

                <div className="conversation-meta">
                  <span className="message-count">
                    {conversation.messages.length} رسالة
                  </span>
                  <span className="last-updated">
                    {formatDate(conversation.updatedAt)}
                  </span>
                </div>

                {conversation.messages.length > 0 && (
                  <div className="conversation-preview">
                    {conversation.messages[conversation.messages.length - 1].content.substring(0, 60)}
                    {conversation.messages[conversation.messages.length - 1].content.length > 60 && '...'}
                  </div>
                )}
              </div>

              {/* قائمة الخيارات */}
              {activeMenu === conversation.id && (
                <div className="conversation-menu">
                  <button
                    className="menu-item"
                    onClick={() => handleExport(conversation.id, 'txt')}
                  >
                    <Download size={14} />
                    تصدير نصي
                  </button>
                  <button
                    className="menu-item"
                    onClick={() => handleExport(conversation.id, 'pdf')}
                  >
                    <Download size={14} />
                    تصدير PDF
                  </button>
                  <button
                    className="menu-item"
                    onClick={() => handleExport(conversation.id, 'json')}
                  >
                    <Download size={14} />
                    تصدير JSON
                  </button>
                  <hr className="menu-divider" />
                  <button
                    className="menu-item danger"
                    onClick={() => handleDelete(conversation.id)}
                  >
                    <Trash2 size={14} />
                    حذف المحادثة
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      {/* رأس الشريط الجانبي */}
      <div className="sidebar-header">
        <div className="app-logo">
          <Bot size={24} />
          {!isCollapsed && <span>بوت الدردشة الذكي</span>}
        </div>

        {!isCollapsed && (
          <button
            className="btn btn-primary new-chat-btn"
            onClick={onCreateConversation}
          >
            <Plus size={16} />
            محادثة جديدة
          </button>
        )}
      </div>

      {/* شريط البحث */}
      {!isCollapsed && (
        <div className="search-container">
          <div className="search-input-wrapper">
            <Search size={16} className="search-icon" />
            <input
              type="text"
              placeholder="البحث في المحادثات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
          </div>
        </div>
      )}

      {/* قائمة المحادثات */}
      <div className="conversations-container">
        {!isCollapsed ? (
          filteredConversations.length === 0 ? (
            <div className="empty-state">
              <MessageSquare size={48} className="empty-icon" />
              <h3>لا توجد محادثات</h3>
              <p>ابدأ محادثة جديدة للبدء</p>
            </div>
          ) : (
            <div className="conversations-groups">
              {renderConversationGroup('اليوم', conversationGroups.today)}
              {renderConversationGroup('أمس', conversationGroups.yesterday)}
              {renderConversationGroup('هذا الأسبوع', conversationGroups.thisWeek)}
              {renderConversationGroup('أقدم', conversationGroups.older)}
            </div>
          )
        ) : (
          <div className="collapsed-conversations">
            {conversations.slice(0, 5).map(conversation => (
              <button
                key={conversation.id}
                className={`collapsed-conversation-item ${
                  currentConversation?.id === conversation.id ? 'active' : ''
                }`}
                onClick={() => onSelectConversation(conversation.id)}
                title={conversation.title}
              >
                <MessageSquare size={20} />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* تذييل الشريط الجانبي */}
      <div className="sidebar-footer">
        <button
          className="footer-btn training-btn"
          onClick={onShowTraining}
          title="التدريب العبقري"
        >
          <Brain size={20} />
          {!isCollapsed && <span>🧠 التدريب العبقري</span>}
        </button>

        <button
          className="footer-btn knowledge-btn"
          onClick={onShowKnowledge}
          title="قاعدة المعرفة"
        >
          <BookOpen size={20} />
          {!isCollapsed && <span>📚 قاعدة المعرفة</span>}
        </button>

        <button
          className="footer-btn"
          onClick={onShowSettings}
          title="الإعدادات"
        >
          <Settings size={20} />
          {!isCollapsed && <span>الإعدادات</span>}
        </button>

        <button
          className="footer-btn collapse-btn"
          onClick={() => setIsCollapsed(!isCollapsed)}
          title={isCollapsed ? 'توسيع الشريط الجانبي' : 'طي الشريط الجانبي'}
        >
          <Calendar size={20} />
        </button>
      </div>

      {/* طبقة الخلفية لإغلاق القوائم */}
      {activeMenu && (
        <div
          className="menu-overlay"
          onClick={() => setActiveMenu(null)}
        />
      )}
    </div>
  )
}

export default Sidebar
