@echo off
chcp 65001 >nul
title 🧠 تشغيل التدريب العبقري - AI Chat Bot

echo.
echo ========================================
echo      🧠 تشغيل التدريب العبقري
echo      AI Chat Bot - النسخة العبقرية
echo ========================================
echo.

echo 📋 الخطوة 1: فحص البيئة...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ Python موجود

echo 📋 الخطوة 2: إنشاء مجلدات التدريب...
if not exist "training" mkdir training
if not exist "training\data" mkdir training\data
if not exist "training\models" mkdir training\models
if not exist "training\configs" mkdir training\configs
if not exist "training\logs" mkdir training\logs

echo 📋 الخطوة 3: تثبيت المكتبات الأساسية...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 --quiet
pip install transformers datasets accelerate peft --quiet
pip install pandas numpy --quiet

echo 📋 الخطوة 4: تحضير LLaMA-Factory...
cd training
if not exist "LLaMA-Factory" (
    echo 🔄 تحميل LLaMA-Factory...
    git clone --depth 1 https://github.com/hiyouga/LLaMA-Factory.git
    if errorlevel 1 (
        echo ⚠️ فشل في تحميل LLaMA-Factory
        echo 💡 سيتم استخدام النسخة المحلية
    )
)

if exist "LLaMA-Factory" (
    cd LLaMA-Factory
    echo 🔄 تثبيت LLaMA-Factory...
    pip install -e . --quiet
    cd ..
)

cd ..

echo 📋 الخطوة 5: إنشاء بيانات تجريبية...
python -c "
import json
import os

# إنشاء بيانات تجريبية عربية
sample_data = [
    {
        'conversations': [
            {'from': 'human', 'value': 'مرحبا، كيف حالك؟'},
            {'from': 'gpt', 'value': 'مرحبا! أنا بخير جداً، شكراً لك على السؤال. كيف يمكنني مساعدتك اليوم؟'}
        ]
    },
    {
        'conversations': [
            {'from': 'human', 'value': 'ما هو الذكاء الاصطناعي؟'},
            {'from': 'gpt', 'value': 'الذكاء الاصطناعي هو مجال في علوم الحاسوب يهدف إلى إنشاء أنظمة قادرة على أداء مهام تتطلب ذكاءً بشرياً، مثل التعلم والتفكير وحل المشكلات.'}
        ]
    },
    {
        'conversations': [
            {'from': 'human', 'value': 'اكتب لي قصيدة قصيرة عن الأمل'},
            {'from': 'gpt', 'value': 'في قلبي نور يضيء الطريق\\nوأمل يزهر كالربيع\\nمهما اشتدت العواصف\\nسأبقى أؤمن بالغد الجميل'}
        ]
    },
    {
        'conversations': [
            {'from': 'human', 'value': 'كيف أتعلم البرمجة؟'},
            {'from': 'gpt', 'value': 'لتعلم البرمجة، ابدأ بلغة سهلة مثل Python، مارس يومياً، اعمل على مشاريع صغيرة، وانضم لمجتمعات المطورين للتعلم والمساعدة.'}
        ]
    },
    {
        'conversations': [
            {'from': 'human', 'value': 'ما أهمية القراءة؟'},
            {'from': 'gpt', 'value': 'القراءة تنمي العقل وتوسع المدارك، تحسن المفردات والتعبير، تقلل التوتر، وتفتح آفاقاً جديدة للمعرفة والخيال.'}
        ]
    }
]

# حفظ البيانات
os.makedirs('training/data', exist_ok=True)
with open('training/data/arabic_genius_sample.json', 'w', encoding='utf-8') as f:
    json.dump(sample_data, f, ensure_ascii=False, indent=2)

print('✅ تم إنشاء بيانات تجريبية عربية')
"

echo 📋 الخطوة 6: اختبار مدرب العبقرية...
python training/genius_trainer.py

echo.
echo 🎉 تم إعداد التدريب العبقري بنجاح!
echo.
echo 🚀 الآن يمكنك:
echo    1. فتح التطبيق
echo    2. الذهاب إلى تبويب "التدريب العبقري"
echo    3. رفع ملفات التدريب
echo    4. بدء التدريب
echo.
echo ========================================
echo      🧠 جاهز للعبقرية!
echo ========================================
pause
