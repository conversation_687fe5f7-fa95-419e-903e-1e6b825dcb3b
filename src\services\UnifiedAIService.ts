import { OpenRouterAPI, LMStudioAPI, AIModel, ChatMessage } from './OpenRouterAPI'

export type AIProvider = 'openrouter' | 'lmstudio'

export interface UnifiedAIConfig {
  openRouterApiKey?: string
  preferredProvider: AIProvider
  fallbackToOpenRouter: boolean
}

export class UnifiedAIService {
  private openRouterAPI: OpenRouterAPI | null = null
  private lmStudioAPI: LMStudioAPI
  private config: UnifiedAIConfig

  constructor(config: UnifiedAIConfig) {
    this.config = config
    this.lmStudioAPI = new LMStudioAPI()
    
    if (config.openRouterApiKey) {
      this.openRouterAPI = new OpenRouterAPI(config.openRouterApiKey)
    }
  }

  // تحديث إعدادات الخدمة
  updateConfig(config: Partial<UnifiedAIConfig>): void {
    this.config = { ...this.config, ...config }
    
    if (config.openRouterApiKey) {
      if (this.openRouterAPI) {
        this.openRouterAPI.updateApiKey(config.openRouterApiKey)
      } else {
        this.openRouterAPI = new OpenRouterAPI(config.openRouterApiKey)
      }
    }
  }

  // فحص توفر الخدمات
  async checkAvailability(): Promise<{
    lmStudio: boolean
    openRouter: boolean
    recommendedProvider: AIProvider
  }> {
    const lmStudioAvailable = await this.lmStudioAPI.checkAvailability()
    const openRouterAvailable = this.openRouterAPI ? await this.openRouterAPI.testConnection() : false

    // تحديد الخدمة المفضلة
    let recommendedProvider: AIProvider = this.config.preferredProvider

    if (this.config.preferredProvider === 'lmstudio' && !lmStudioAvailable && openRouterAvailable) {
      recommendedProvider = 'openrouter'
    } else if (this.config.preferredProvider === 'openrouter' && !openRouterAvailable && lmStudioAvailable) {
      recommendedProvider = 'lmstudio'
    }

    return {
      lmStudio: lmStudioAvailable,
      openRouter: openRouterAvailable,
      recommendedProvider
    }
  }

  // الحصول على جميع النماذج المتاحة
  async getAllModels(): Promise<{
    local: AIModel[]
    online: AIModel[]
    combined: AIModel[]
  }> {
    const localModels = await this.lmStudioAPI.getLocalModels()
    const onlineModels = this.openRouterAPI ? await this.openRouterAPI.getAvailableModels() : []

    return {
      local: localModels,
      online: onlineModels,
      combined: [...localModels, ...onlineModels]
    }
  }

  // إرسال رسالة مع اختيار الخدمة المناسبة
  async sendMessage(
    message: string,
    model: string,
    conversationHistory: ChatMessage[] = [],
    forceProvider?: AIProvider
  ): Promise<{
    response: string
    usedProvider: AIProvider
    modelUsed: string
  }> {
    const availability = await this.checkAvailability()
    
    // تحديد الخدمة المستخدمة
    let providerToUse: AIProvider = forceProvider || availability.recommendedProvider

    // التحقق من توفر النموذج المطلوب
    const isLocalModel = model.includes('🏠') || await this.isModelLocal(model)
    
    if (isLocalModel && !availability.lmStudio) {
      if (availability.openRouter && this.config.fallbackToOpenRouter) {
        providerToUse = 'openrouter'
        // استخدام نموذج بديل من OpenRouter
        model = 'meta-llama/llama-3.3-8b-instruct:free'
      } else {
        throw new Error('النموذج المحلي غير متاح و LM Studio غير مشغل')
      }
    }

    try {
      let response: string
      let actualModel = model

      if (providerToUse === 'lmstudio' && availability.lmStudio) {
        response = await this.lmStudioAPI.sendMessage(message, model, conversationHistory)
      } else if (providerToUse === 'openrouter' && availability.openRouter && this.openRouterAPI) {
        response = await this.openRouterAPI.sendMessage(message, model, conversationHistory)
      } else {
        throw new Error('لا توجد خدمة متاحة للرد على الرسالة')
      }

      return {
        response,
        usedProvider: providerToUse,
        modelUsed: actualModel
      }
    } catch (error) {
      // محاولة التبديل للخدمة البديلة
      if (this.config.fallbackToOpenRouter && providerToUse === 'lmstudio' && availability.openRouter && this.openRouterAPI) {
        console.log('🔄 التبديل إلى OpenRouter بسبب خطأ في LM Studio')
        const response = await this.openRouterAPI.sendMessage(
          message, 
          'meta-llama/llama-3.3-8b-instruct:free', 
          conversationHistory
        )
        return {
          response,
          usedProvider: 'openrouter',
          modelUsed: 'meta-llama/llama-3.3-8b-instruct:free'
        }
      }

      throw error
    }
  }

  // فحص ما إذا كان النموذج محلي
  private async isModelLocal(modelId: string): Promise<boolean> {
    const localModels = await this.lmStudioAPI.getLocalModels()
    return localModels.some(model => model.id === modelId)
  }

  // الحصول على حالة الخدمات
  async getServicesStatus(): Promise<{
    lmStudio: {
      available: boolean
      modelsCount: number
      status: string
    }
    openRouter: {
      available: boolean
      hasApiKey: boolean
      status: string
    }
  }> {
    const availability = await this.checkAvailability()
    const localModels = await this.lmStudioAPI.getLocalModels()

    return {
      lmStudio: {
        available: availability.lmStudio,
        modelsCount: localModels.length,
        status: availability.lmStudio ? 
          `✅ متصل - ${localModels.length} نموذج متاح` : 
          '❌ غير متصل - تأكد من تشغيل LM Studio'
      },
      openRouter: {
        available: availability.openRouter,
        hasApiKey: !!this.openRouterAPI,
        status: availability.openRouter ? 
          '✅ متصل - جاهز للاستخدام' : 
          this.openRouterAPI ? '❌ خطأ في الاتصال' : '⚠️ لم يتم تعيين مفتاح API'
      }
    }
  }

  // تشغيل LM Studio تلقائياً
  async launchLMStudio(): Promise<boolean> {
    try {
      const { spawn } = require('child_process')
      const lmStudioPath = 'C:\\Program Files\\LM Studio\\LM Studio.exe'
      
      spawn(lmStudioPath, [], {
        detached: true,
        stdio: 'ignore'
      }).unref()

      // انتظار قليل ثم فحص التوفر
      await new Promise(resolve => setTimeout(resolve, 5000))
      return await this.lmStudioAPI.checkAvailability()
    } catch (error) {
      console.error('خطأ في تشغيل LM Studio:', error)
      return false
    }
  }
}

// إنشاء مثيل مشترك
export const unifiedAI = new UnifiedAIService({
  preferredProvider: 'lmstudio',
  fallbackToOpenRouter: true
})
