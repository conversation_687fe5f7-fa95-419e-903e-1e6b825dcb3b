import React from 'react'
import { <PERSON>, <PERSON>, <PERSON>own, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import './EmotionalIndicator.css'

interface EmotionalState {
  primary: string
  secondary: string[]
  intensity: number
  confidence: number
  suggestions: string[]
}

interface EmotionalIndicatorProps {
  emotionalState: EmotionalState | null
  isVisible: boolean
  onClose?: () => void
}

const EmotionalIndicator: React.FC<EmotionalIndicatorProps> = ({
  emotionalState,
  isVisible,
  onClose
}) => {
  if (!isVisible || !emotionalState) return null

  const getEmotionIcon = (emotion: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      happiness: <Sun size={20} className="emotion-icon happiness" />,
      love: <Heart size={20} className="emotion-icon love" />,
      excitement: <Zap size={20} className="emotion-icon excitement" />,
      sadness: <Cloud size={20} className="emotion-icon sadness" />,
      anger: <Frown size={20} className="emotion-icon anger" />,
      anxiety: <Brain size={20} className="emotion-icon anxiety" />,
      loneliness: <Star size={20} className="emotion-icon loneliness" />,
      nostalgia: <Heart size={20} className="emotion-icon nostalgia" />,
      hope: <Smile size={20} className="emotion-icon hope" />
    }
    
    return iconMap[emotion] || <Brain size={20} className="emotion-icon neutral" />
  }

  const getEmotionLabel = (emotion: string): string => {
    const labelMap: { [key: string]: string } = {
      happiness: 'سعادة',
      love: 'حب',
      excitement: 'حماس',
      sadness: 'حزن',
      anger: 'غضب',
      anxiety: 'قلق',
      loneliness: 'وحدة',
      nostalgia: 'حنين',
      hope: 'أمل',
      neutral: 'محايد'
    }
    
    return labelMap[emotion] || emotion
  }

  const getEmotionColor = (emotion: string): string => {
    const colorMap: { [key: string]: string } = {
      happiness: '#FFD700',
      love: '#FF69B4',
      excitement: '#FF4500',
      sadness: '#4682B4',
      anger: '#DC143C',
      anxiety: '#9370DB',
      loneliness: '#708090',
      nostalgia: '#DDA0DD',
      hope: '#32CD32',
      neutral: '#808080'
    }
    
    return colorMap[emotion] || '#808080'
  }

  const intensityPercentage = Math.round(emotionalState.intensity * 100)
  const confidencePercentage = Math.round(emotionalState.confidence * 100)

  return (
    <div className="emotional-indicator">
      <div className="emotional-card">
        {/* رأس البطاقة */}
        <div className="emotional-header">
          <div className="emotional-title">
            {getEmotionIcon(emotionalState.primary)}
            <span>تحليل المشاعر</span>
          </div>
          {onClose && (
            <button className="close-btn" onClick={onClose}>
              ×
            </button>
          )}
        </div>

        {/* المشاعر الأساسية */}
        <div className="primary-emotion">
          <div className="emotion-info">
            <span className="emotion-label">
              {getEmotionLabel(emotionalState.primary)}
            </span>
            <div className="emotion-metrics">
              <div className="metric">
                <span className="metric-label">الشدة:</span>
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ 
                      width: `${intensityPercentage}%`,
                      backgroundColor: getEmotionColor(emotionalState.primary)
                    }}
                  />
                </div>
                <span className="metric-value">{intensityPercentage}%</span>
              </div>
              <div className="metric">
                <span className="metric-label">الثقة:</span>
                <div className="progress-bar">
                  <div 
                    className="progress-fill confidence"
                    style={{ width: `${confidencePercentage}%` }}
                  />
                </div>
                <span className="metric-value">{confidencePercentage}%</span>
              </div>
            </div>
          </div>
        </div>

        {/* المشاعر الثانوية */}
        {emotionalState.secondary.length > 0 && (
          <div className="secondary-emotions">
            <span className="section-title">مشاعر أخرى:</span>
            <div className="emotion-tags">
              {emotionalState.secondary.map((emotion, index) => (
                <span 
                  key={index}
                  className="emotion-tag"
                  style={{ borderColor: getEmotionColor(emotion) }}
                >
                  {getEmotionLabel(emotion)}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* الاقتراحات */}
        {emotionalState.suggestions.length > 0 && (
          <div className="suggestions">
            <span className="section-title">💡 اقتراحات:</span>
            <ul className="suggestions-list">
              {emotionalState.suggestions.slice(0, 2).map((suggestion, index) => (
                <li key={index} className="suggestion-item">
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* رسالة تشجيعية */}
        <div className="emotional-message">
          {getEmotionalMessage(emotionalState.primary)}
        </div>
      </div>
    </div>
  )
}

// رسائل تشجيعية حسب المشاعر
const getEmotionalMessage = (emotion: string): string => {
  const messages: { [key: string]: string } = {
    happiness: '🌟 استمتع بهذه اللحظات الجميلة!',
    love: '💕 الحب يجعل الحياة أكثر جمالاً',
    excitement: '🚀 حماسك معدي ورائع!',
    sadness: '🤗 تذكر أن الحزن مؤقت والفرج قريب',
    anger: '🧘‍♂️ خذ نفساً عميقاً، كل شيء سيكون بخير',
    anxiety: '💪 أنت أقوى مما تعتقد',
    loneliness: '🌙 لست وحيداً، نحن هنا معك',
    nostalgia: '📸 الذكريات الجميلة كنز لا يفنى',
    hope: '🌅 الأمل نور يضيء طريق المستقبل',
    neutral: '😊 أهلاً بك، كيف يمكنني مساعدتك؟'
  }
  
  return messages[emotion] || messages.neutral
}

export default EmotionalIndicator
