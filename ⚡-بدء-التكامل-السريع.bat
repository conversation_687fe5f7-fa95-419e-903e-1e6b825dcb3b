@echo off
chcp 65001 >nul
title ⚡ بدء التكامل السريع - Bolt.diy + مشروعي

echo.
echo ========================================
echo      ⚡ بدء التكامل السريع
echo      دمج أفضل ميزات Bolt.diy
echo ========================================
echo.

echo 📋 الخطوة 1: تشغيل التكامل الكامل...
call "🔗-تكامل-Bolt-مع-مشروعي.bat"

echo.
echo 📋 الخطوة 2: تحليل المكتبات المطلوبة...
if exist "bolt-integration\dependency_analysis.json" (
    echo ✅ تم العثور على تحليل المكتبات
    python -c "
import json

try:
    with open('bolt-integration/dependency_analysis.json', 'r', encoding='utf-8') as f:
        analysis = json.load(f)
    
    print('📦 المكتبات الجديدة المطلوبة:')
    print('=' * 40)
    
    new_deps = analysis.get('new_dependencies', {})
    for dep, version in new_deps.items():
        print(f'  ✅ {dep}@{version}')
    
    print(f'\\n📊 إجمالي المكتبات الجديدة: {len(new_deps)}')
    
    # إنشاء أمر التثبيت
    if new_deps:
        install_cmd = 'npm install ' + ' '.join([f'{dep}@{version}' for dep, version in new_deps.items()])
        
        with open('bolt-integration/install_command.txt', 'w', encoding='utf-8') as f:
            f.write(install_cmd)
        
        print(f'\\n💡 أمر التثبيت محفوظ في: bolt-integration/install_command.txt')
        print(f'📝 الأمر: {install_cmd[:100]}...')

except Exception as e:
    print(f'❌ خطأ في قراءة التحليل: {e}')
"
) else (
    echo ❌ لم يتم العثور على تحليل المكتبات
    echo 💡 تأكد من تشغيل التكامل الكامل أولاً
)

echo.
echo 📋 الخطوة 3: إنشاء خطة التنفيذ السريعة...
python -c "
import os
from datetime import datetime

def create_quick_plan():
    plan = f'''# ⚡ خطة التنفيذ السريعة
## 📅 تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🎯 الهدف
دمج أهم ميزات Bolt.diy مع مشروعنا في أسرع وقت ممكن.

## 🚀 خطوات التنفيذ (30 دقيقة)

### ⏰ الدقائق 1-10: إعداد المكتبات
```bash
# تثبيت المكتبات الأساسية
npm install @vercel/ai @unocss/core @monaco-editor/react
npm install framer-motion lucide-react clsx
npm install --save-dev @unocss/vite
```

### ⏰ الدقائق 11-20: دمج ModelSelector
1. نسخ `ModelSelector.tsx` إلى `src/components/`
2. تحديث imports في `ChatInterface.tsx`
3. إضافة دعم النماذج المتعددة
4. اختبار التكامل

### ⏰ الدقائق 21-30: تحسين الواجهة
1. إضافة UnoCSS إلى `vite.config.ts`
2. تحديث الأنماط الأساسية
3. إضافة مكونات UI محسنة
4. اختبار نهائي

## 📝 ملفات للتعديل

### 1️⃣ package.json
```json
{{
  "dependencies": {{
    "@vercel/ai": "^3.0.0",
    "@unocss/core": "^0.58.0",
    "@monaco-editor/react": "^4.6.0",
    "framer-motion": "^10.0.0",
    "lucide-react": "^0.300.0"
  }},
  "devDependencies": {{
    "@unocss/vite": "^0.58.0"
  }}
}}
```

### 2️⃣ vite.config.ts
```typescript
import UnoCSS from 'unocss/vite'

export default defineConfig({{
  plugins: [
    react(),
    UnoCSS(), // إضافة UnoCSS
  ],
}})
```

### 3️⃣ src/components/ChatInterface.tsx
```typescript
import {{ ModelSelector }} from './ModelSelector'

// إضافة في المكون
<ModelSelector
  model={{selectedModel}}
  setModel={{setSelectedModel}}
  provider={{selectedProvider}}
  setProvider={{setSelectedProvider}}
/>
```

## 🎯 النتائج المتوقعة
- ✅ دعم نماذج متعددة (OpenAI, Claude, Gemini)
- ✅ واجهة محسنة مع UnoCSS
- ✅ مكونات UI متطورة
- ✅ أداء أفضل وتجربة مستخدم محسنة

## 🔧 استكشاف الأخطاء

### خطأ في imports
```typescript
// إذا كان هناك خطأ في المسارات
import {{ ModelSelector }} from '../bolt-integration/components/chat/ModelSelector'
```

### خطأ في CSS
```typescript
// تأكد من إضافة UnoCSS في vite.config.ts
import UnoCSS from 'unocss/vite'
```

### خطأ في TypeScript
```typescript
// إضافة types مفقودة
npm install --save-dev @types/react @types/node
```

---
**⚡ ابدأ الآن واستمتع بتطبيق AI متطور!** 🚀
'''
    
    with open('bolt-integration/QUICK_IMPLEMENTATION.md', 'w', encoding='utf-8') as f:
        f.write(plan)
    
    print('✅ تم إنشاء خطة التنفيذ السريعة')

try:
    create_quick_plan()
except Exception as e:
    print(f'❌ خطأ: {e}')
"

echo.
echo 📋 الخطوة 4: إنشاء ملفات البداية السريعة...

echo 🔄 إنشاء ModelSelector مبسط...
python -c "
model_selector_content = '''import React, { useState } from 'react';

interface Model {
  id: string;
  name: string;
  provider: string;
}

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  models: Model[];
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  models
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className=\"model-selector\" style={{ position: 'relative', marginBottom: '16px' }}>
      <label style={{ display: 'block', marginBottom: '8px', fontWeight: 600 }}>
        🤖 اختر النموذج الذكي:
      </label>
      
      <button
        onClick={() => setIsOpen(!isOpen)}
        style={{
          width: '100%',
          padding: '12px 16px',
          border: '2px solid #e1e5e9',
          borderRadius: '8px',
          background: 'white',
          cursor: 'pointer',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <span>{models.find(m => m.id === selectedModel)?.name || 'اختر نموذج'}</span>
        <span>{isOpen ? '▲' : '▼'}</span>
      </button>

      {isOpen && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: 0,
          right: 0,
          background: 'white',
          border: '2px solid #e1e5e9',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          zIndex: 1000,
          maxHeight: '200px',
          overflowY: 'auto'
        }}>
          {models.map((model) => (
            <button
              key={model.id}
              onClick={() => {
                onModelChange(model.id);
                setIsOpen(false);
              }}
              style={{
                width: '100%',
                padding: '12px 16px',
                border: 'none',
                background: selectedModel === model.id ? '#f0f8ff' : 'white',
                cursor: 'pointer',
                textAlign: 'right',
                borderBottom: '1px solid #f0f0f0'
              }}
            >
              <div>
                <div style={{ fontWeight: 600 }}>{model.name}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>{model.provider}</div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ModelSelector;
'''

with open('bolt-integration/ModelSelector-Simple.tsx', 'w', encoding='utf-8') as f:
    f.write(model_selector_content)

print('✅ تم إنشاء ModelSelector مبسط')
"

echo 🔄 إنشاء ملف تكوين UnoCSS...
python -c "
uno_config = '''import { defineConfig } from 'unocss'

export default defineConfig({
  // إعدادات UnoCSS للتطبيق العربي
  theme: {
    colors: {
      primary: '#667eea',
      secondary: '#764ba2',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3'
    },
    fontFamily: {
      arabic: ['Segoe UI', 'Tahoma', 'Arial', 'sans-serif']
    }
  },
  shortcuts: {
    // اختصارات CSS مفيدة
    'btn-primary': 'bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors',
    'btn-secondary': 'bg-secondary text-white px-4 py-2 rounded-lg hover:bg-secondary/90 transition-colors',
    'card': 'bg-white rounded-lg shadow-lg p-6 border border-gray-200',
    'input-field': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary'
  },
  rules: [
    // قواعد مخصصة للعربية
    ['rtl', { direction: 'rtl' }],
    ['text-arabic', { 'font-family': 'Segoe UI, Tahoma, Arial, sans-serif' }]
  ]
})
'''

with open('bolt-integration/uno.config.ts', 'w', encoding='utf-8') as f:
    f.write(uno_config)

print('✅ تم إنشاء تكوين UnoCSS')
"

echo.
echo 🎉 تم إكمال التحضير للتكامل السريع!
echo.
echo 📊 الملفات الجاهزة:
echo ===================
echo ✅ bolt-integration/ - مجلد التكامل الكامل
echo ✅ QUICK_IMPLEMENTATION.md - خطة التنفيذ السريعة
echo ✅ ModelSelector-Simple.tsx - مكون مبسط للبداية
echo ✅ uno.config.ts - تكوين UnoCSS
echo ✅ install_command.txt - أوامر التثبيت
echo.
echo 🚀 الخطوات التالية:
echo ==================
echo 1. راجع ملف QUICK_IMPLEMENTATION.md
echo 2. نفذ أوامر التثبيت من install_command.txt
echo 3. ابدأ بدمج ModelSelector-Simple.tsx
echo 4. أضف UnoCSS للتصميم المتقدم
echo 5. اختبر واستمتع بالنتائج!
echo.
echo 💡 نصيحة: ابدأ بالمكونات البسيطة ثم انتقل للمعقدة
echo.
echo ========================================
echo      ⚡ جاهز للتكامل السريع!
echo ========================================
pause
