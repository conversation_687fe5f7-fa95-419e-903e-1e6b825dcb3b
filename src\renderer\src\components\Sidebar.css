.sidebar {
  width: 320px;
  height: 100vh;
  background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  z-index: 100;
}

.sidebar.collapsed {
  width: 70px;
}

/* رأس الشريط الجانبي */
.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-primary);
}

.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.collapsed .app-logo span {
  display: none;
}

.new-chat-btn {
  width: 100%;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  font-weight: 500;
}

.collapsed .new-chat-btn {
  display: none;
}

/* شريط البحث */
.search-container {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: var(--spacing-md);
  color: var(--text-secondary);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) calc(var(--spacing-md) * 2.5);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.search-input::placeholder {
  color: var(--text-secondary);
}

/* حاوية المحادثات */
.conversations-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md) 0;
}

/* حالة فارغة */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-icon {
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.empty-state p {
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

/* مجموعات المحادثات */
.conversations-groups {
  padding: 0 var(--spacing-md);
}

.conversation-group {
  margin-bottom: var(--spacing-lg);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-secondary);
}

.group-title {
  color: var(--text-primary);
}

.group-count {
  background-color: var(--primary-color);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* قائمة المحادثات */
.conversations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.conversation-item {
  position: relative;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
  border: 1px solid transparent;
}

.conversation-item:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  transform: translateX(-2px);
}

.conversation-item.active {
  background: linear-gradient(135deg, var(--primary-color), #0056b3);
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--box-shadow);
}

.conversation-item.active .conversation-title,
.conversation-item.active .conversation-meta,
.conversation-item.active .conversation-preview {
  color: inherit;
}

.conversation-content {
  width: 100%;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.conversation-title {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
  margin-left: var(--spacing-sm);
}

.conversation-menu-btn {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-secondary);
  opacity: 0;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.conversation-item:hover .conversation-menu-btn {
  opacity: 1;
}

.conversation-menu-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

.conversation-item.active .conversation-menu-btn {
  color: white;
}

.conversation-item.active .conversation-menu-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.conversation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.message-count {
  font-weight: 500;
}

.last-updated {
  opacity: 0.8;
}

.conversation-preview {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.3;
  opacity: 0.8;
}

/* قائمة خيارات المحادثة */
.conversation-menu {
  position: absolute;
  top: 100%;
  left: var(--spacing-md);
  right: var(--spacing-md);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  z-index: 1000;
  overflow: hidden;
  animation: menuSlide 0.2s ease-out;
}

@keyframes menuSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  text-align: right;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: var(--bg-secondary);
}

.menu-item.danger {
  color: var(--danger-color);
}

.menu-item.danger:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.menu-divider {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: 0;
}

.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* المحادثات المطوية */
.collapsed-conversations {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-sm);
}

.collapsed-conversation-item {
  width: 100%;
  height: 40px;
  border: none;
  background: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.collapsed-conversation-item:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

.collapsed-conversation-item.active {
  background-color: var(--primary-color);
  color: white;
}

/* تذييل الشريط الجانبي */
.sidebar-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.collapsed .sidebar-footer {
  align-items: center;
}

.footer-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
  width: 100%;
  justify-content: flex-start;
}

.collapsed .footer-btn {
  justify-content: center;
  padding: var(--spacing-sm);
}

.collapsed .footer-btn span {
  display: none;
}

.footer-btn:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

.collapse-btn {
  margin-top: var(--spacing-sm);
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-md);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebar.collapsed {
    width: 320px;
    transform: translateX(100%);
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100vw;
  }

  .sidebar-header {
    padding: var(--spacing-md);
  }

  .search-container {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .conversations-groups {
    padding: 0 var(--spacing-sm);
  }

  .conversation-item {
    padding: var(--spacing-sm);
  }
}

/* تحسين شريط التمرير */
.conversations-container::-webkit-scrollbar {
  width: 6px;
}

.conversations-container::-webkit-scrollbar-track {
  background: transparent;
}

.conversations-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.conversations-container::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* تأثيرات إضافية */
.conversation-item {
  animation: fadeIn 0.3s ease-in;
}

.conversation-group {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تحسين إمكانية الوصول */
.conversation-item:focus,
.footer-btn:focus,
.menu-item:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* تحسين النصوص الطويلة */
.conversation-title,
.conversation-preview {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* زر التدريب العبقري */
.footer-btn.training-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  font-weight: 600;
  margin-bottom: 8px;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.footer-btn.training-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.footer-btn.training-btn:hover::before {
  left: 100%;
}

.footer-btn.training-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.footer-btn.training-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.2);
}
