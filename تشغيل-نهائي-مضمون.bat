@echo off
chcp 65001 >nul
title تشغيل نهائي مضمون

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  🎯 تشغيل نهائي مضمون 100%%                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 إصلاح المشاكل...

:: تثبيت electron و typescript سريع
echo 📦 تثبيت electron و typescript...
npm install electron typescript --save-dev --no-optional --silent --force

:: فحص النتيجة
if exist "node_modules\.bin\electron.cmd" (
    echo ✅ Electron مثبت بنجاح
) else (
    echo ⚠️ Electron لم يثبت، سنحاول طريقة أخرى...
)

echo.
echo 🚀 تشغيل التطبيق...

:: تجربة طرق مختلفة
echo 🔄 الطريقة 1: npx electron
npx electron . 2>nul
if not errorlevel 1 (
    echo ✅ نجح التشغيل!
    goto :success
)

echo 🔄 الطريقة 2: node_modules\.bin\electron
if exist "node_modules\.bin\electron.cmd" (
    node_modules\.bin\electron.cmd .
    if not errorlevel 1 (
        echo ✅ نجح التشغيل!
        goto :success
    )
)

echo 🔄 الطريقة 3: تثبيت electron عالمي
npm install -g electron --silent
electron . 2>nul
if not errorlevel 1 (
    echo ✅ نجح التشغيل!
    goto :success
)

echo ❌ فشل في جميع الطرق
echo 💡 المشكلة قد تكون في:
echo    - اتصال الإنترنت
echo    - صلاحيات النظام
echo    - مشاكل في npm
goto :end

:success
echo 🎉 تم تشغيل التطبيق بنجاح!

:end
echo.
echo 📝 انتهى التشغيل
pause
