@echo off
echo ========================================
echo      بوت الدردشة الذكي - AI Chat Bot
echo ========================================
echo.

echo جاري التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js مثبت بنجاح!
echo.

echo جاري التحقق من التبعيات...
if not exist "node_modules" (
    echo جاري تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات!
        pause
        exit /b 1
    )
) else (
    echo التبعيات مثبتة مسبقاً.
)

echo.
echo جاري تشغيل التطبيق...
echo يرجى الانتظار حتى يفتح التطبيق...
echo.

npm run dev

if %errorlevel% neq 0 (
    echo.
    echo خطأ في تشغيل التطبيق!
    echo يرجى التحقق من الأخطاء أعلاه.
    pause
)
