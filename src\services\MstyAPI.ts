import axios, { AxiosInstance } from 'axios'

/**
 * خدمة الاتصال مع Msty
 * Msty هو تطبيق محلي للذكاء الاصطناعي مشابه لـ LM Studio
 */
export class MstyAPI {
  private apiClient: AxiosInstance
  private baseURL = 'http://localhost:10000/v1' // منفذ Msty الافتراضي
  private isAvailable = false

  constructor() {
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer msty-local-key' // مفتاح محلي افتراضي
      },
      timeout: 30000
    })
  }

  // فحص توفر Msty
  async checkAvailability(): Promise<boolean> {
    try {
      // محاولة الاتصال بـ Msty
      const response = await this.apiClient.get('/models', { timeout: 5000 })
      this.isAvailable = response.status === 200
      console.log('✅ Msty متاح ويعمل')
      return this.isAvailable
    } catch (error) {
      // محاولة منافذ أخرى محتملة
      const alternatePorts = [8080, 3000, 5000, 11434]
      
      for (const port of alternatePorts) {
        try {
          const altClient = axios.create({
            baseURL: `http://localhost:${port}/v1`,
            timeout: 3000
          })
          
          const response = await altClient.get('/models')
          if (response.status === 200) {
            this.baseURL = `http://localhost:${port}/v1`
            this.apiClient.defaults.baseURL = this.baseURL
            this.isAvailable = true
            console.log(`✅ Msty متاح على المنفذ ${port}`)
            return true
          }
        } catch (portError) {
          continue
        }
      }
      
      this.isAvailable = false
      console.log('❌ Msty غير متاح')
      return false
    }
  }

  // الحصول على النماذج المتاحة
  async getModels(): Promise<any[]> {
    try {
      if (!this.isAvailable) {
        await this.checkAvailability()
      }

      if (!this.isAvailable) {
        return []
      }

      const response = await this.apiClient.get('/models')
      const models = response.data.data || response.data.models || []
      
      return models.map((model: any) => ({
        id: model.id || model.name,
        name: model.id || model.name,
        description: model.description || `نموذج Msty: ${model.id || model.name}`,
        provider: 'msty',
        type: 'local',
        available: true
      }))
    } catch (error) {
      console.error('خطأ في جلب نماذج Msty:', error)
      return []
    }
  }

  // إرسال رسالة للدردشة
  async sendMessage(messages: any[], model: string): Promise<any> {
    try {
      if (!this.isAvailable) {
        throw new Error('Msty غير متاح')
      }

      const response = await this.apiClient.post('/chat/completions', {
        model: model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: false
      })

      return {
        success: true,
        message: response.data.choices[0].message.content,
        usage: response.data.usage
      }
    } catch (error) {
      console.error('خطأ في إرسال رسالة لـ Msty:', error)
      return {
        success: false,
        error: error.message || 'خطأ في الاتصال مع Msty'
      }
    }
  }

  // إرسال رسالة مع تدفق (streaming)
  async sendMessageStream(messages: any[], model: string, onChunk: (chunk: string) => void): Promise<any> {
    try {
      if (!this.isAvailable) {
        throw new Error('Msty غير متاح')
      }

      const response = await this.apiClient.post('/chat/completions', {
        model: model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: true
      }, {
        responseType: 'stream'
      })

      let fullMessage = ''

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n')
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            
            if (data === '[DONE]') {
              return
            }
            
            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content
              
              if (content) {
                fullMessage += content
                onChunk(content)
              }
            } catch (parseError) {
              // تجاهل أخطاء التحليل
            }
          }
        }
      })

      return new Promise((resolve) => {
        response.data.on('end', () => {
          resolve({
            success: true,
            message: fullMessage
          })
        })

        response.data.on('error', (error: any) => {
          resolve({
            success: false,
            error: error.message
          })
        })
      })
    } catch (error) {
      console.error('خطأ في إرسال رسالة متدفقة لـ Msty:', error)
      return {
        success: false,
        error: error.message || 'خطأ في الاتصال مع Msty'
      }
    }
  }

  // فحص حالة النموذج
  async getModelInfo(modelId: string): Promise<any> {
    try {
      const response = await this.apiClient.get(`/models/${modelId}`)
      return {
        success: true,
        model: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  // تحديث إعدادات الاتصال
  updateConnection(baseURL: string, apiKey?: string): void {
    this.baseURL = baseURL
    this.apiClient.defaults.baseURL = baseURL
    
    if (apiKey) {
      this.apiClient.defaults.headers['Authorization'] = `Bearer ${apiKey}`
    }
  }

  // الحصول على معلومات الخادم
  async getServerInfo(): Promise<any> {
    try {
      const response = await this.apiClient.get('/health')
      return {
        success: true,
        info: response.data,
        baseURL: this.baseURL,
        available: this.isAvailable
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        baseURL: this.baseURL,
        available: false
      }
    }
  }
}

// إنشاء مثيل مشترك
export const mstyAPI = new MstyAPI()
