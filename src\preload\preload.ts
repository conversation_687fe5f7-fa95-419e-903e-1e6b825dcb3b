import { contextBridge, ipcRenderer } from 'electron'

// تعريف واجهة API الآمنة
export interface ElectronAPI {
  // إرسال رسالة للذكاء الاصطناعي
  sendMessage: (data: { message: string; model: string; conversationId: string }) => Promise<any>

  // إدارة النماذج
  getAvailableModels: () => Promise<any>

  // إدارة المحادثات
  createConversation: (title: string) => Promise<any>
  getConversations: () => Promise<any>
  getConversation: (conversationId: string) => Promise<any>
  deleteConversation: (conversationId: string) => Promise<any>

  // تصدير المحادثات
  exportConversation: (data: { conversationId: string; format: string }) => Promise<any>

  // إدارة الإعدادات
  saveSettings: (settings: any) => Promise<any>
  getSettings: () => Promise<any>

  // الخدمة الموحدة الجديدة
  updateAISettings: (settings: any) => Promise<any>
  getServicesStatus: () => Promise<any>
  launchLMStudio: () => Promise<any>
}

// كشف واجهة API الآمنة للمحتوى المعروض
const electronAPI: ElectronAPI = {
  sendMessage: (data) => ipcRenderer.invoke('send-message', data),
  getAvailableModels: () => ipcRenderer.invoke('get-available-models'),
  createConversation: (title) => ipcRenderer.invoke('create-conversation', title),
  getConversations: () => ipcRenderer.invoke('get-conversations'),
  getConversation: (conversationId) => ipcRenderer.invoke('get-conversation', conversationId),
  deleteConversation: (conversationId) => ipcRenderer.invoke('delete-conversation', conversationId),
  exportConversation: (data) => ipcRenderer.invoke('export-conversation', data),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  getSettings: () => ipcRenderer.invoke('get-settings'),

  // الطرق الجديدة للخدمة الموحدة
  updateAISettings: (settings) => ipcRenderer.invoke('update-ai-settings', settings),
  getServicesStatus: () => ipcRenderer.invoke('get-services-status'),
  launchLMStudio: () => ipcRenderer.invoke('launch-lm-studio'),
}

contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// تعريف النوع للنافذة
declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
