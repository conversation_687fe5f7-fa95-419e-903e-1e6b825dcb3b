@echo off
title AI Chat Bot

echo ========================================
echo           AI Chat Bot
echo        Desktop Application
echo ========================================
echo.

echo Starting AI Chat Bot...
echo.

:: Try different methods to run the app
echo Method 1: Direct electron...
npx electron .
if not errorlevel 1 goto success

echo Method 2: Build and run...
npx tsc -p tsconfig.main.json
npx electron .
if not errorlevel 1 goto success

echo Method 3: Install and run...
call ULTIMATE-FIX.bat
goto end

:success
echo.
echo AI Chat Bot started successfully!
goto end

:end
echo.
pause
