# 🚀 Release v0.0.7

## What's Changed 🌟

### 🔄 Changes since v0.0.6

### ✨ Features

* added support for reasoning content ([#1168](https://github.com/stackblitz-labs/bolt.diy/pull/1168)) by @thecodacus
* add deepseek-r1-distill-llama-70b to groq provider ([#1187](https://github.com/stackblitz-labs/bolt.diy/pull/1187)) by @saif78642
* add Gemini 2.0 Flash-thinking-exp-01-21 model with 65k token support ([#1202](https://github.com/stackblitz-labs/bolt.diy/pull/1202)) by @saif78642
* added more dynamic models, sorted and remove duplicate models ([#1206](https://github.com/stackblitz-labs/bolt.diy/pull/1206)) by @thecodacus
* support for <think></think> tags to allow reasoning tokens formatted in UI ([#1205](https://github.com/stackblitz-labs/bolt.diy/pull/1205)) by @thecodacus
* enhanced Code Context and Project Summary Features ([#1191](https://github.com/stackblitz-labs/bolt.diy/pull/1191)) by @thecodacus
* added dynamic model support for openAI provider ([#1241](https://github.com/stackblitz-labs/bolt.diy/pull/1241)) by @thecodacus
* bolt dyi new settings UI V3 ([#1245](https://github.com/stackblitz-labs/bolt.diy/pull/1245)) by @Stijnus
* implement llm model search ([#1322](https://github.com/stackblitz-labs/bolt.diy/pull/1322)) by @kamilfurtak
* diff-view-v2-no-conflict ([#1335](https://github.com/stackblitz-labs/bolt.diy/pull/1335)) by @Toddyclipsgg
* netlify one click deployment ([#1376](https://github.com/stackblitz-labs/bolt.diy/pull/1376)) by @xKevIsDev
* diff view v3 ([#1367](https://github.com/stackblitz-labs/bolt.diy/pull/1367)) by @Toddyclipsgg
* added anthropic dynamic models ([#1374](https://github.com/stackblitz-labs/bolt.diy/pull/1374)) by @thecodacus
* make user made changes persistent after reload ([#1387](https://github.com/stackblitz-labs/bolt.diy/pull/1387)) by @thecodacus


### 🐛 Bug Fixes

* docker prod env variable fix ([#1170](https://github.com/stackblitz-labs/bolt.diy/pull/1170)) by @thecodacus
* improve push to github option ([#1111](https://github.com/stackblitz-labs/bolt.diy/pull/1111)) by @thecodacus
* git import issue when importing bolt on bolt ([#1020](https://github.com/stackblitz-labs/bolt.diy/pull/1020)) by @thecodacus
* issue with alternate message when importing from folder and git ([#1216](https://github.com/stackblitz-labs/bolt.diy/pull/1216)) by @thecodacus
* tune the system prompt to avoid diff writing ([#1218](https://github.com/stackblitz-labs/bolt.diy/pull/1218)) by @thecodacus
* removed chrome canary note (6a8449e) by @leex279
* starter template icons fix and auto resize of custon icons are reverted ([#1298](https://github.com/stackblitz-labs/bolt.diy/pull/1298)) by @thecodacus
* auto scroll fix, scroll allow user to scroll up during ai response ([#1299](https://github.com/stackblitz-labs/bolt.diy/pull/1299)) by @thecodacus
* bug fix New UI / Feature tab - Default values hard-coded (294adfd) by @leex279
* debounce profile update notifications to prevent toast spam (70b723d) by @xKevIsDev
* bolt dyi UI bugfix ([#1342](https://github.com/stackblitz-labs/bolt.diy/pull/1342)) by @Stijnus
* preserve complete provider settings in cookies (220e2da) by @xKevIsDev
* for remove settings icon _index.tsx ([#1356](https://github.com/stackblitz-labs/bolt.diy/pull/1356)) by @Stijnus
* fix enhance prompt to stop implementing full project instead of enhancing  ([#1383](https://github.com/stackblitz-labs/bolt.diy/pull/1383)) by @thecodacus


### ⚙️ CI

* updated Dockerfile to install latest version of corepack to ensure to have the right version to pnpm (c88938c) by @BaptisteCDC


### 🔍 Other Changes

* new anthropogenic model for amazon bedrock (0fd039b) by @leex279
* This reverts commit 871aefbe83c31660b32b53b63772ebba33ed7954, reversing ([#1335](https://github.com/stackblitz-labs/bolt.diy/pull/1335)) by @Toddyclipsgg
* Update docker.yaml (stable/main deployment) (f0ea22e) by @leex279
* Update Dockerfile - Test Bugfix Dockerpipeline (8e790d0) by @leex279
* Update Dockerfile (5297081) by @leex279
* Update docker.yaml (7dda793) by @leex279
* Update docker.yaml (67c4051) by @leex279
* Fix broken astro project git clone ([#1352](https://github.com/stackblitz-labs/bolt.diy/pull/1352)) by @Phr33d0m


## ✨ First-time Contributors

A huge thank you to our amazing new contributors! Your first contribution marks the start of an exciting journey! 🌟

* 🌟 [@BaptisteCDC](https://github.com/BaptisteCDC)
* 🌟 [@Phr33d0m](https://github.com/Phr33d0m)
* 🌟 [@kamilfurtak](https://github.com/kamilfurtak)
* 🌟 [@saif78642](https://github.com/saif78642)
* 🌟 [@xKevIsDev](https://github.com/xKevIsDev)

## 📈 Stats

**Full Changelog**: [`v0.0.6..v0.0.7`](https://github.com/stackblitz-labs/bolt.diy/compare/v0.0.6...v0.0.7)
