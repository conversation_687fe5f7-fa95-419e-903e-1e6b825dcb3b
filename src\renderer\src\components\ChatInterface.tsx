import React, { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Copy, Download, RotateCcw } from 'lucide-react'
// import ReactMarkdown from 'react-markdown'
// import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
// import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { Conversation, ChatMessage, AIModel, AppSettings } from '../types'
import ModelSelector from './ModelSelector'
import MessageInput from './MessageInput'
import EmotionalIndicator from './EmotionalIndicator'
import './ChatInterface.css'

interface ChatInterfaceProps {
  conversation: Conversation | null
  availableModels: AIModel[]
  settings: AppSettings
  onSendMessage: (message: string, model: string) => Promise<any>
  onCreateConversation: () => void
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversation,
  availableModels,
  settings,
  onSendMessage,
  onCreateConversation
}) => {
  const [selectedModel, setSelectedModel] = useState(
    settings.default_model || 'meta-llama/llama-3.3-8b-instruct:free'
  )
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [emotionalState, setEmotionalState] = useState<any>(null)
  const [showEmotionalIndicator, setShowEmotionalIndicator] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // التمرير إلى أسفل عند إضافة رسائل جديدة
  useEffect(() => {
    scrollToBottom()
  }, [conversation?.messages])

  // تحديث النموذج المحدد عند تغيير الإعدادات
  useEffect(() => {
    if (settings.default_model) {
      setSelectedModel(settings.default_model)
    }
  }, [settings.default_model])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (message: string) => {
    if (!message.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const result = await onSendMessage(message, selectedModel)

      if (!result.success) {
        setError(result.error || 'حدث خطأ أثناء إرسال الرسالة')
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      setError('حدث خطأ غير متوقع')
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // يمكن إضافة إشعار نجح النسخ هنا
    } catch (error) {
      console.error('فشل في نسخ النص:', error)
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: '2-digit'
    })
  }

  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user'

    return (
      <div
        key={message.id}
        className={`message ${isUser ? 'message-user' : 'message-assistant'} fade-in`}
      >
        <div className="message-avatar">
          {isUser ? (
            <User size={20} />
          ) : (
            <Bot size={20} />
          )}
        </div>

        <div className="message-content">
          <div className="message-header">
            <span className="message-role">
              {isUser ? 'أنت' : 'المساعد الذكي'}
            </span>
            {settings.show_timestamps && (
              <span className="message-timestamp">
                {formatTimestamp(message.timestamp)}
              </span>
            )}
          </div>

          <div className="message-text">
            <p style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
              {message.content}
            </p>
          </div>

          <div className="message-actions">
            <button
              className="btn-icon"
              onClick={() => copyToClipboard(message.content)}
              title="نسخ النص"
            >
              <Copy size={16} />
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="chat-interface">
      {/* المؤشر العاطفي */}
      <EmotionalIndicator
        emotionalState={emotionalState}
        isVisible={showEmotionalIndicator}
        onClose={() => setShowEmotionalIndicator(false)}
      />

      {/* شريط الأدوات العلوي */}
      <div className="chat-header">
        <div className="chat-title">
          <h2>{conversation?.title || 'مرحباً بك في بوت الدردشة الذكي'}</h2>
          {conversation && (
            <span className="message-count">
              {conversation.messages.length} رسالة
            </span>
          )}
        </div>

        <div className="chat-controls">
          <ModelSelector
            models={availableModels}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
          />

          <button
            className="btn btn-outline btn-sm"
            onClick={onCreateConversation}
            title="محادثة جديدة"
          >
            <RotateCcw size={16} />
            محادثة جديدة
          </button>
        </div>
      </div>

      {/* منطقة الرسائل */}
      <div className="messages-container" ref={messagesContainerRef}>
        {conversation?.messages.length === 0 ? (
          <div className="welcome-message">
            <div className="welcome-content">
              <Bot size={48} className="welcome-icon" />
              <h3>مرحباً بك في بوت الدردشة الذكي</h3>
              <p>
                يمكنك الآن بدء محادثة مع الذكاء الاصطناعي. اختر النموذج المناسب واكتب رسالتك أدناه.
              </p>
              <div className="welcome-features">
                <div className="feature">
                  <span>🤖</span>
                  <span>نماذج ذكاء اصطناعي متقدمة</span>
                </div>
                <div className="feature">
                  <span>🌐</span>
                  <span>دعم اللغة العربية والإنجليزية</span>
                </div>
                <div className="feature">
                  <span>💾</span>
                  <span>حفظ المحادثات تلقائياً</span>
                </div>
                <div className="feature">
                  <span>🔒</span>
                  <span>خصوصية وأمان عاليين</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="messages-list">
            {conversation.messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </div>
        )}

        {isLoading && (
          <div className="typing-indicator">
            <div className="typing-avatar">
              <Bot size={20} />
            </div>
            <div className="typing-content">
              <div className="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span className="typing-text">المساعد يكتب...</span>
            </div>
          </div>
        )}
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="error-message">
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      {/* منطقة إدخال الرسائل */}
      <div className="chat-input-container">
        <MessageInput
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
          placeholder="اكتب رسالتك هنا..."
        />
      </div>
    </div>
  )
}

export default ChatInterface
