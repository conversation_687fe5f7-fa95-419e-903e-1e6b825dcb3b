{"name": "ai-chat-bot", "version": "1.0.0", "description": "Advanced AI Chat Bot for Windows with OpenRouter API integration", "main": "dist/main/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "tsc -p tsconfig.main.json && electron .", "dev:simple": "electron .", "dev:direct": "tsc -p tsconfig.main.json && electron .", "dev:skip-ts": "electron .", "dev:force": "node_modules\\.bin\\electron.cmd .", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "pack": "electron-builder --dir", "dist": "electron-builder", "postinstall": "electron-builder install-app-deps"}, "keywords": ["ai", "chatbot", "electron", "openrouter", "windows"], "author": "AI Chat Bot Developer", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.3.3", "vite": "^5.0.8"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2", "electron-store": "^8.1.0", "crypto-js": "^4.2.0", "lucide-react": "^0.294.0", "uuid": "^9.0.1"}, "build": {"appId": "com.aichatbot.app", "productName": "AI Chat Bot", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}