@echo off
chcp 65001 >nul
title تشغيل فوري للتطبيق

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ⚡ تشغيل فوري للتطبيق                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 تشغيل مباشر بدون انتظار...
echo.

:: البحث عن Electron في أماكن مختلفة
if exist "node_modules\.bin\electron.cmd" (
    echo ✅ وجدت Electron في .bin
    echo 🚀 تشغيل التطبيق...
    node_modules\.bin\electron.cmd .
    goto :end
)

if exist "node_modules\electron\dist\electron.exe" (
    echo ✅ وجدت Electron في dist
    echo 🚀 تشغيل التطبيق...
    node_modules\electron\dist\electron.exe .
    goto :end
)

:: البحث في مجلدات أخرى
for /d %%i in (node_modules\*electron*) do (
    if exist "%%i\dist\electron.exe" (
        echo ✅ وجدت Electron في %%i
        echo 🚀 تشغيل التطبيق...
        "%%i\dist\electron.exe" .
        goto :end
    )
)

:: تجربة تشغيل مباشر
echo 🔄 محاولة تشغيل مباشر...
electron . 2>nul
if errorlevel 1 (
    echo ❌ لم أجد Electron
    echo 💡 جرب تشغيل: npm install electron
    echo 💡 أو استخدم: تشغيل-ذكي.bat
) else (
    echo ✅ تم التشغيل بنجاح!
)

:end
echo.
echo 📝 انتهى التشغيل
pause
