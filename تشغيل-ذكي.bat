@echo off
chcp 65001 >nul
title تشغيل التطبيق - الإصدار الذكي

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 تشغيل التطبيق الذكي                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص البيئة...

:: فحص Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت!
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

:: فحص npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo 🔧 إصلاح المشاكل...

:: تثبيت التبعيات المفقودة
echo 📦 تثبيت Vite و TypeScript...
npm install vite @vitejs/plugin-react typescript @types/node @types/react @types/react-dom --save-dev --silent

:: تثبيت تبعيات TypeScript الإضافية
echo 📦 تثبيت تبعيات TypeScript...
npm install @types/electron concurrently --save-dev --silent

:: تنظيف cache
echo 🧹 تنظيف cache...
npm cache clean --force --silent

echo.
echo 🚀 تشغيل التطبيق...

:: تشغيل المشروع
npm run dev

echo.
echo 📝 انتهى التشغيل
pause
