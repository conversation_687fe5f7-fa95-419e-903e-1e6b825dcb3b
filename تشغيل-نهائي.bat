@echo off
chcp 65001 >nul
title تشغيل التطبيق - الحل النهائي

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🌟 الحل النهائي العبقري                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 تحليل الوضع...

:: فحص وجود node_modules
if exist "node_modules" (
    echo ✅ node_modules موجود
    goto :run_app
) else (
    echo ⚠️ node_modules مفقود
    goto :install_deps
)

:install_deps
echo.
echo 📦 تثبيت التبعيات...
echo 🔄 هذا قد يستغرق بضع دقائق...
echo.

:: تثبيت سريع للتبعيات الأساسية فقط
npm install electron react react-dom --save --no-optional --no-audit --no-fund --silent

:: فحص نجاح التثبيت
if exist "node_modules\electron" (
    echo ✅ تم تثبيت Electron
    goto :run_app
) else (
    echo ❌ فشل في تثبيت التبعيات
    echo 💡 جرب تشغيل: npm install يدوياً
    pause
    exit /b 1
)

:run_app
echo.
echo 🚀 تشغيل التطبيق...
echo.

:: تشغيل Electron مباشرة
if exist "node_modules\.bin\electron.cmd" (
    echo 🔄 تشغيل Electron...
    node_modules\.bin\electron.cmd .
) else if exist "node_modules\electron\dist\electron.exe" (
    echo 🔄 تشغيل Electron...
    node_modules\electron\dist\electron.exe .
) else (
    echo ❌ Electron غير موجود
    echo 💡 جرب تشغيل: npm install electron
    pause
    exit /b 1
)

echo.
echo 📝 انتهى التشغيل
pause
