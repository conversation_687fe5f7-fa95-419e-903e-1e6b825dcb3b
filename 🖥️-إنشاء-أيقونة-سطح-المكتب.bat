@echo off
chcp 65001 >nul
title إنشاء أيقونة سطح المكتب - AI Chat Bot

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🖥️ إنشاء أيقونة سطح المكتب 🖥️                ║
echo ║                   AI Chat Bot                                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 إنشاء أيقونة احترافية لسطح المكتب...
echo.

:: الحصول على مسار سطح المكتب
set "DESKTOP=%USERPROFILE%\Desktop"
set "PROJECT_PATH=%CD%"

echo 📁 مسار سطح المكتب: %DESKTOP%
echo 📁 مسار المشروع: %PROJECT_PATH%
echo.

echo 🎨 إنشاء ملف الأيقونة...

:: إنشاء ملف batch للتشغيل السريع
echo @echo off > "%DESKTOP%\🤖 AI Chat Bot.bat"
echo title AI Chat Bot - بوت الدردشة الذكي >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo cd /d "%PROJECT_PATH%" >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo. >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo ╔══════════════════════════════════════════════════════════════╗ >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo ║                🤖 AI Chat Bot 🤖                            ║ >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo ║                بوت الدردشة الذكي                           ║ >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo ╚══════════════════════════════════════════════════════════════╝ >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo. >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo 🚀 جاري تشغيل التطبيق... >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo echo. >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo npx electron . >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo if errorlevel 1 ( >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo     echo ❌ فشل في التشغيل! >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo     echo 💡 جرب تشغيل ULTIMATE-FIX.bat أولاً >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo     pause >> "%DESKTOP%\🤖 AI Chat Bot.bat"
echo ^) >> "%DESKTOP%\🤖 AI Chat Bot.bat"

echo ✅ تم إنشاء ملف التشغيل على سطح المكتب!
echo.

:: إنشاء ملف إصلاح سريع على سطح المكتب
echo 🔧 إنشاء ملف الإصلاح السريع...

echo @echo off > "%DESKTOP%\🔧 إصلاح AI Chat Bot.bat"
echo title إصلاح AI Chat Bot >> "%DESKTOP%\🔧 إصلاح AI Chat Bot.bat"
echo cd /d "%PROJECT_PATH%" >> "%DESKTOP%\🔧 إصلاح AI Chat Bot.bat"
echo echo. >> "%DESKTOP%\🔧 إصلاح AI Chat Bot.bat"
echo echo 🔧 إصلاح وتشغيل AI Chat Bot... >> "%DESKTOP%\🔧 إصلاح AI Chat Bot.bat"
echo echo. >> "%DESKTOP%\🔧 إصلاح AI Chat Bot.bat"
echo call ULTIMATE-FIX.bat >> "%DESKTOP%\🔧 إصلاح AI Chat Bot.bat"

echo ✅ تم إنشاء ملف الإصلاح على سطح المكتب!
echo.

:: إنشاء ملف معلومات
echo 📄 إنشاء ملف المعلومات...

echo ╔══════════════════════════════════════════════════════════════╗ > "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo ║                    🤖 AI Chat Bot 🤖                        ║ >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo ║                   بوت الدردشة الذكي                        ║ >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo ╚══════════════════════════════════════════════════════════════╝ >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo. >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 🎯 الوصف: >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo تطبيق ذكي للدردشة مع الذكاء الاصطناعي >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo يدعم نماذج متعددة من OpenRouter >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo. >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 🚀 طريقة التشغيل: >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 1. اضغط مرتين على "🤖 AI Chat Bot.bat" >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 2. إذا لم يعمل، اضغط على "🔧 إصلاح AI Chat Bot.bat" >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 3. انتظر حتى يكتمل التثبيت والتشغيل >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo. >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 💡 نصائح: >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo • تأكد من اتصال الإنترنت >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo • يحتاج Node.js للعمل >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo • يمكن تغيير النموذج من الإعدادات >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo. >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 📧 للدعم: اتصل بالمطور >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"
echo 🌐 المشروع: %PROJECT_PATH% >> "%DESKTOP%\📖 معلومات AI Chat Bot.txt"

echo ✅ تم إنشاء ملف المعلومات على سطح المكتب!
echo.

echo 🎉 تم إنشاء جميع الملفات بنجاح!
echo.
echo 📋 الملفات المُنشأة على سطح المكتب:
echo    🤖 AI Chat Bot.bat - للتشغيل المباشر
echo    🔧 إصلاح AI Chat Bot.bat - للإصلاح والتشغيل
echo    📖 معلومات AI Chat Bot.txt - معلومات التطبيق
echo.

echo 💡 لإنشاء أيقونة جميلة:
echo    1. افتح assets\create-icon.html في المتصفح
echo    2. اضغط "تحميل الأيقونة"
echo    3. احفظها كـ icon.png في مجلد assets
echo.

echo 🚀 الآن يمكنك تشغيل التطبيق من سطح المكتب!
echo.

:: فتح سطح المكتب لإظهار الملفات الجديدة
explorer "%DESKTOP%"

echo ✨ انتهى إنشاء الأيقونات!
pause
