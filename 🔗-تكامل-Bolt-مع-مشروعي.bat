@echo off
chcp 65001 >nul
title 🔗 تكامل Bolt.diy مع مشروعي - التطوير المتقدم

echo.
echo ========================================
echo      🔗 تكامل Bolt.diy مع مشروعي
echo      نقل أفضل الميزات والمكونات
echo ========================================
echo.

set "BOLT_PATH=E:\bolt.diy-0.0.7\bolt.diy-0.0.7"
set "PROJECT_PATH=%CD%"

echo 📋 الخطوة 1: فحص مسارات المشاريع...
if not exist "%BOLT_PATH%" (
    echo ❌ مجلد Bolt.diy غير موجود في: %BOLT_PATH%
    echo 💡 تأكد من المسار الصحيح
    pause
    exit /b 1
)

echo ✅ Bolt.diy موجود في: %BOLT_PATH%
echo ✅ مشروعي موجود في: %PROJECT_PATH%

echo 📋 الخطوة 2: إنشاء مجلدات التكامل...
if not exist "bolt-integration" mkdir bolt-integration
if not exist "bolt-integration\components" mkdir bolt-integration\components
if not exist "bolt-integration\lib" mkdir bolt-integration\lib
if not exist "bolt-integration\styles" mkdir bolt-integration\styles
if not exist "bolt-integration\types" mkdir bolt-integration\types

echo 📋 الخطوة 3: نسخ المكونات المفيدة...

echo 🔄 نسخ مكونات الدردشة المتطورة...
if exist "%BOLT_PATH%\app\components\chat" (
    xcopy "%BOLT_PATH%\app\components\chat\*.tsx" "bolt-integration\components\chat\" /E /I /Y >nul 2>&1
    xcopy "%BOLT_PATH%\app\components\chat\*.scss" "bolt-integration\components\chat\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مكونات الدردشة
) else (
    echo ❌ مجلد chat غير موجود
)

echo 🔄 نسخ مكونات المحرر...
if exist "%BOLT_PATH%\app\components\editor" (
    xcopy "%BOLT_PATH%\app\components\editor\*.*" "bolt-integration\components\editor\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مكونات المحرر
) else (
    echo ❌ مجلد editor غير موجود
)

echo 🔄 نسخ مكونات الواجهة...
if exist "%BOLT_PATH%\app\components\ui" (
    xcopy "%BOLT_PATH%\app\components\ui\*.*" "bolt-integration\components\ui\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مكونات الواجهة
) else (
    echo ❌ مجلد ui غير موجود
)

echo 🔄 نسخ مكونات الشريط الجانبي...
if exist "%BOLT_PATH%\app\components\sidebar" (
    xcopy "%BOLT_PATH%\app\components\sidebar\*.*" "bolt-integration\components\sidebar\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مكونات الشريط الجانبي
) else (
    echo ❌ مجلد sidebar غير موجود
)

echo 📋 الخطوة 4: نسخ المكتبات والأدوات...

echo 🔄 نسخ مكتبات النماذج...
if exist "%BOLT_PATH%\app\lib" (
    xcopy "%BOLT_PATH%\app\lib\*.*" "bolt-integration\lib\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ المكتبات
) else (
    echo ❌ مجلد lib غير موجود
)

echo 🔄 نسخ أنواع البيانات...
if exist "%BOLT_PATH%\app\types" (
    xcopy "%BOLT_PATH%\app\types\*.*" "bolt-integration\types\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ أنواع البيانات
) else (
    echo ❌ مجلد types غير موجود
)

echo 🔄 نسخ الأنماط...
if exist "%BOLT_PATH%\app\styles" (
    xcopy "%BOLT_PATH%\app\styles\*.*" "bolt-integration\styles\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ الأنماط
) else (
    echo ❌ مجلد styles غير موجود
)

echo 📋 الخطوة 5: نسخ ملفات التكوين المهمة...

echo 🔄 نسخ package.json...
if exist "%BOLT_PATH%\package.json" (
    copy "%BOLT_PATH%\package.json" "bolt-integration\bolt-package.json" >nul 2>&1
    echo ✅ تم نسخ package.json
)

echo 🔄 نسخ vite.config.ts...
if exist "%BOLT_PATH%\vite.config.ts" (
    copy "%BOLT_PATH%\vite.config.ts" "bolt-integration\bolt-vite.config.ts" >nul 2>&1
    echo ✅ تم نسخ vite.config.ts
)

echo 🔄 نسخ uno.config.ts...
if exist "%BOLT_PATH%\uno.config.ts" (
    copy "%BOLT_PATH%\uno.config.ts" "bolt-integration\bolt-uno.config.ts" >nul 2>&1
    echo ✅ تم نسخ uno.config.ts
)

echo 🔄 نسخ tsconfig.json...
if exist "%BOLT_PATH%\tsconfig.json" (
    copy "%BOLT_PATH%\tsconfig.json" "bolt-integration\bolt-tsconfig.json" >nul 2>&1
    echo ✅ تم نسخ tsconfig.json
)

echo 📋 الخطوة 6: تحليل المكتبات المطلوبة...
python -c "
import json
import os

def analyze_dependencies():
    bolt_package_path = 'bolt-integration/bolt-package.json'
    current_package_path = 'package.json'
    
    analysis = {
        'new_dependencies': {},
        'new_dev_dependencies': {},
        'scripts_to_add': {},
        'config_updates': []
    }
    
    # قراءة package.json من Bolt
    if os.path.exists(bolt_package_path):
        with open(bolt_package_path, 'r', encoding='utf-8') as f:
            bolt_package = json.load(f)
        
        # قراءة package.json الحالي
        current_package = {}
        if os.path.exists(current_package_path):
            with open(current_package_path, 'r', encoding='utf-8') as f:
                current_package = json.load(f)
        
        current_deps = current_package.get('dependencies', {})
        current_dev_deps = current_package.get('devDependencies', {})
        
        # تحديد المكتبات الجديدة المفيدة
        useful_deps = [
            '@vercel/ai', '@ai-sdk/openai', '@ai-sdk/anthropic',
            '@unocss/core', '@unocss/preset-uno', '@unocss/vite',
            '@monaco-editor/react', 'monaco-editor',
            '@remix-run/react', '@remix-run/node',
            'react-hotkeys-hook', 'react-split-pane',
            'framer-motion', 'lucide-react',
            'clsx', 'tailwind-merge'
        ]
        
        bolt_deps = bolt_package.get('dependencies', {})
        bolt_dev_deps = bolt_package.get('devDependencies', {})
        
        # إضافة المكتبات المفيدة
        for dep in useful_deps:
            if dep in bolt_deps and dep not in current_deps:
                analysis['new_dependencies'][dep] = bolt_deps[dep]
            elif dep in bolt_dev_deps and dep not in current_dev_deps:
                analysis['new_dev_dependencies'][dep] = bolt_dev_deps[dep]
        
        # إضافة سكريبتات مفيدة
        bolt_scripts = bolt_package.get('scripts', {})
        useful_scripts = ['dev', 'build', 'preview', 'typecheck']
        
        for script in useful_scripts:
            if script in bolt_scripts:
                analysis['scripts_to_add'][f'bolt:{script}'] = bolt_scripts[script]
        
        # تحديثات التكوين
        analysis['config_updates'] = [
            'إضافة UnoCSS للتصميم المتقدم',
            'تكوين Monaco Editor للكود',
            'إعداد Vercel AI SDK',
            'تحديث TypeScript configs'
        ]
    
    # حفظ التحليل
    with open('bolt-integration/dependency_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    
    print('✅ تم تحليل المكتبات المطلوبة')
    print(f'📦 مكتبات جديدة: {len(analysis[\"new_dependencies\"])}')
    print(f'🔧 مكتبات تطوير: {len(analysis[\"new_dev_dependencies\"])}')
    print(f'📜 سكريبتات جديدة: {len(analysis[\"scripts_to_add\"])}')

try:
    analyze_dependencies()
except Exception as e:
    print(f'❌ خطأ في التحليل: {e}')
"

echo 📋 الخطوة 7: إنشاء دليل التكامل...
python -c "
import os
from datetime import datetime

def create_integration_guide():
    guide_content = '''# 🔗 دليل تكامل Bolt.diy مع مشروعي

## 📅 تاريخ التكامل: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''

## 🎯 الهدف
دمج أفضل ميزات Bolt.diy مع مشروع AI Chat Bot لإنشاء تطبيق متطور ومتميز.

## 📁 الملفات المنسوخة

### 🎨 مكونات الواجهة
- **chat/**: مكونات الدردشة المتطورة
- **editor/**: محرر الكود المدمج  
- **ui/**: مكونات الواجهة الأساسية
- **sidebar/**: الشريط الجانبي المتقدم

### 🔧 المكتبات والأدوات
- **lib/**: مكتبات الذكاء الاصطناعي والأدوات
- **types/**: تعريفات TypeScript
- **styles/**: أنماط CSS متقدمة

### ⚙️ ملفات التكوين
- **bolt-package.json**: قائمة المكتبات
- **bolt-vite.config.ts**: تكوين Vite
- **bolt-uno.config.ts**: تكوين UnoCSS
- **bolt-tsconfig.json**: تكوين TypeScript

## 🚀 خطوات التطبيق

### 1️⃣ تحديث المكتبات
```bash
# راجع ملف dependency_analysis.json
# ثم قم بتثبيت المكتبات الجديدة
npm install @vercel/ai @unocss/core @monaco-editor/react
```

### 2️⃣ دمج المكونات
```typescript
// استيراد مكونات Bolt.diy
import { ModelSelector } from './bolt-integration/components/chat/ModelSelector';
import { CodeEditor } from './bolt-integration/components/editor/CodeEditor';
```

### 3️⃣ تحديث التكوينات
- دمج vite.config.ts مع التكوين الحالي
- إضافة UnoCSS للتصميم
- تحديث TypeScript configs

### 4️⃣ اختبار التكامل
- تشغيل التطبيق والتأكد من عمل الميزات الجديدة
- اختبار المكونات المدمجة
- تحسين الأداء والتوافق

## 💡 الميزات الجديدة المتوقعة

### 🤖 ذكاء اصطناعي متطور
- دعم نماذج متعددة في واجهة واحدة
- محرك توجيهات محسن
- تكامل مع Vercel AI SDK

### 💻 محرر كود مدمج
- تمييز الصيغة للغات متعددة
- إكمال تلقائي للكود
- تشغيل الكود في الوقت الفعلي

### 🎨 واجهة محسنة
- تصميم عصري مع UnoCSS
- حركات سلسة وتأثيرات
- سمات متعددة (فاتح/مظلم)

### 📁 إدارة ملفات متقدمة
- مستكشف ملفات تفاعلي
- رفع وتحميل الملفات
- تكامل مع Git

## ⚠️ ملاحظات مهمة

### 🔧 التوافق
- تأكد من توافق إصدارات React
- راجع تعارضات CSS
- اختبر على متصفحات مختلفة

### 🎯 الأولويات
1. دمج ModelSelector أولاً
2. إضافة محرر الكود
3. تحسين الواجهة
4. إضافة ميزات متقدمة

### 📚 المراجع
- [Bolt.diy Documentation](https://github.com/stackblitz-labs/bolt.diy)
- [Vercel AI SDK](https://sdk.vercel.ai/)
- [UnoCSS](https://unocss.dev/)
- [Monaco Editor](https://microsoft.github.io/monaco-editor/)

---

**🎉 مبروك! الآن لديك كل ما تحتاجه لإنشاء تطبيق AI متطور!** 🚀
'''
    
    with open('bolt-integration/INTEGRATION_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print('✅ تم إنشاء دليل التكامل')

try:
    create_integration_guide()
except Exception as e:
    print(f'❌ خطأ في إنشاء الدليل: {e}')
"

echo.
echo 🎉 تم إكمال تكامل Bolt.diy بنجاح!
echo.
echo 📊 النتائج:
echo ==========
echo ✅ تم نسخ مكونات الدردشة المتطورة
echo ✅ تم نسخ محرر الكود المدمج
echo ✅ تم نسخ مكونات الواجهة
echo ✅ تم نسخ المكتبات والأدوات
echo ✅ تم تحليل المكتبات المطلوبة
echo ✅ تم إنشاء دليل التكامل
echo.
echo 📁 الملفات المنشأة:
echo ==================
echo 📂 bolt-integration/components/ - المكونات المنسوخة
echo 📂 bolt-integration/lib/ - المكتبات والأدوات
echo 📂 bolt-integration/styles/ - الأنماط المتقدمة
echo 📄 bolt-integration/dependency_analysis.json - تحليل المكتبات
echo 📄 bolt-integration/INTEGRATION_GUIDE.md - دليل التكامل
echo.
echo 🚀 الخطوات التالية:
echo ==================
echo 1. راجع ملف INTEGRATION_GUIDE.md
echo 2. اطلع على dependency_analysis.json
echo 3. ابدأ دمج المكونات المفيدة
echo 4. اختبر الميزات الجديدة
echo 5. استمتع بتطبيق AI متطور!
echo.
echo ========================================
echo      🔗 تكامل مكتمل بنجاح!
echo ========================================
pause
