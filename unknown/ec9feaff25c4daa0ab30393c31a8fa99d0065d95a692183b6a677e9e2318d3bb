import React, { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import Sidebar from './components/Sidebar'
import SettingsPanel from './components/SettingsPanel'
// 🔥 إزالة استيراد LoadingScreen نهائياً
import { Conversation } from './types'
import './App.css'

interface AppState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  isLoading: boolean
  showSettings: boolean
  settings: any
  availableModels: any[]
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    conversations: [],
    currentConversation: null,
    isLoading: false, // 🔥 إزالة شاشة التحميل نهائياً
    showSettings: false,
    settings: { theme: 'dark', language: 'ar' },
    availableModels: [
      { id: 'local-model', name: 'النموذج المحلي', provider: 'local' }
    ]
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    console.log('🚀 بدء تهيئة التطبيق...')

    // تطبيق الثيم الافتراضي فوراً
    document.documentElement.setAttribute('data-theme', 'dark')

    console.log('✅ تم إنجاز التهيئة بنجاح - التطبيق جاهز!')
  }

  // إنشاء محادثة جديدة (مبسط)
  const createNewConversation = async (title?: string) => {
    console.log('🆕 إنشاء محادثة جديدة...')

    const newConversation = {
      id: Date.now().toString(),
      title: title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`,
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    setState(prev => ({
      ...prev,
      conversations: [newConversation, ...prev.conversations],
      currentConversation: newConversation
    }))

    console.log('✅ تم إنشاء محادثة جديدة:', newConversation.title)
  }

  // تحديد المحادثة الحالية (مبسط)
  const selectConversation = async (conversationId: string) => {
    const conversation = state.conversations.find(c => c.id === conversationId)
    if (conversation) {
      setState(prev => ({ ...prev, currentConversation: conversation }))
      console.log('✅ تم تحديد المحادثة:', conversation.title)
    }
  }

  // حذف محادثة (مبسط)
  const deleteConversation = async (conversationId: string) => {
    setState(prev => {
      const updatedConversations = prev.conversations.filter(c => c.id !== conversationId)
      const newCurrent = prev.currentConversation?.id === conversationId
        ? updatedConversations[0] || null
        : prev.currentConversation

      return {
        ...prev,
        conversations: updatedConversations,
        currentConversation: newCurrent
      }
    })
    console.log('🗑️ تم حذف المحادثة')
  }

  // إرسال رسالة (مبسط)
  const sendMessage = async (message: string, model: string) => {
    console.log('📤 إرسال رسالة:', message)

    // إنشاء محادثة جديدة إذا لم تكن موجودة
    if (!state.currentConversation) {
      await createNewConversation()
    }

    try {
      // إنشاء رسالة المستخدم
      const userMessage = {
        id: Date.now().toString(),
        role: 'user' as const,
        content: message,
        timestamp: new Date().toISOString()
      }

      // إنشاء رد تجريبي من البوت
      const botMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant' as const,
        content: `مرحباً! تلقيت رسالتك: "${message}". هذا رد تجريبي. سيتم ربط الخدمات الحقيقية قريباً.`,
        timestamp: new Date().toISOString()
      }

      // تحديث المحادثة الحالية
      if (state.currentConversation) {
        const updatedConversation = {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, userMessage, botMessage],
          updatedAt: new Date().toISOString()
        }

        setState(prev => ({
          ...prev,
          currentConversation: updatedConversation,
          conversations: prev.conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedConversation : conv
          )
        }))
      }

      return { success: true }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      return { success: false, error: error.message }
    }
  }

  // حفظ الإعدادات (مبسط)
  const saveSettings = async (newSettings: any) => {
    setState(prev => ({ ...prev, settings: { ...prev.settings, ...newSettings } }))

    // تطبيق السمة الجديدة
    if (newSettings.theme) {
      document.documentElement.setAttribute('data-theme', newSettings.theme)
    }

    console.log('⚙️ تم حفظ الإعدادات:', newSettings)
    return { success: true }
  }

  // تصدير محادثة (مبسط)
  const exportConversation = async (conversationId: string, format: string) => {
    console.log('📤 تصدير المحادثة:', conversationId, format)
    // سيتم تطبيق التصدير الحقيقي لاحقاً
    return { success: true, message: 'سيتم تطبيق التصدير قريباً' }
  }

  // 🔥 إزالة شاشة التحميل نهائياً - الدخول مباشرة للواجهة الرئيسية

  return (
    <div className="app">
      <div className="app-container">
        {/* الشريط الجانبي */}
        <Sidebar
          conversations={state.conversations}
          currentConversation={state.currentConversation}
          onSelectConversation={selectConversation}
          onCreateConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onExportConversation={exportConversation}
          onShowSettings={() => setState(prev => ({ ...prev, showSettings: true }))}
        />

        {/* واجهة الدردشة الرئيسية */}
        <div className="main-content">
          <ChatInterface
            conversation={state.currentConversation}
            availableModels={state.availableModels}
            settings={state.settings}
            onSendMessage={sendMessage}
            onCreateConversation={createNewConversation}
          />
        </div>

        {/* لوحة الإعدادات */}
        {state.showSettings && (
          <SettingsPanel
            settings={state.settings}
            availableModels={state.availableModels}
            onSaveSettings={saveSettings}
            onClose={() => setState(prev => ({ ...prev, showSettings: false }))}
          />
        )}
      </div>
    </div>
  )
}

export default App
