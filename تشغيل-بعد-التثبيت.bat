@echo off
chcp 65001 >nul
title تشغيل بوت الدردشة الذكي
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    بوت الدردشة الذكي                        ║
echo ║                 تشغيل بعد اكتمال التثبيت                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/3] التحقق من اكتمال التثبيت...
if not exist "node_modules" (
    echo ❌ التثبيت لم يكتمل بعد!
    echo 💡 يرجى انتظار اكتمال التثبيت أولاً
    echo 📝 شغل ملف "تشغيل-التطبيق.bat" وانتظر
    pause
    exit /b 1
)
echo ✅ التثبيت مكتمل!

echo.
echo [2/3] بناء الملفات...
echo 🔨 جاري تحضير التطبيق...
call npm run build:main
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء الملفات!
    echo 💡 جرب تشغيل: npm run build:main
    pause
    exit /b 1
)

echo.
echo [3/3] تشغيل التطبيق...
echo 🚀 جاري فتح بوت الدردشة الذكي...
echo.
echo 💡 سيفتح التطبيق في المتصفح على:
echo    http://localhost:3000
echo.
echo ⏳ انتظر 30-60 ثانية حتى يبدأ الخادوم...
echo.

start "" npm run dev

timeout /t 5 /nobreak >nul

echo ✅ تم تشغيل الخادوم!
echo.
echo 🌐 إذا لم يفتح التطبيق تلقائياً:
echo    1. افتح المتصفح
echo    2. اذهب إلى: http://localhost:3000
echo    3. أو جرب: http://localhost:5173
echo.
echo ❓ للمساعدة: اقرأ ملف "حل-المشاكل.md"
echo.
pause
