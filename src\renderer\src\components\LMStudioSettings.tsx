import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  Wifi, 
  WifiOff, 
  Server, 
  Play, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Home,
  Globe
} from 'lucide-react'

interface LMStudioSettingsProps {
  onClose: () => void
}

interface ServiceStatus {
  lmStudio: {
    available: boolean
    modelsCount: number
    status: string
  }
  openRouter: {
    available: boolean
    hasApiKey: boolean
    status: string
  }
}

interface AIModels {
  local: any[]
  online: any[]
  combined: any[]
}

export const LMStudioSettings: React.FC<LMStudioSettingsProps> = ({ onClose }) => {
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null)
  const [models, setModels] = useState<AIModels | null>(null)
  const [loading, setLoading] = useState(true)
  const [openRouterKey, setOpenRouterKey] = useState('')
  const [preferredProvider, setPreferredProvider] = useState<'lmstudio' | 'openrouter'>('lmstudio')
  const [fallbackEnabled, setFallbackEnabled] = useState(true)

  // تحديث حالة الخدمات
  const updateStatus = async () => {
    setLoading(true)
    try {
      const status = await window.electronAPI.getServicesStatus()
      const availableModels = await window.electronAPI.getAvailableModels()
      
      setServiceStatus(status)
      setModels(availableModels)
    } catch (error) {
      console.error('خطأ في تحديث الحالة:', error)
    } finally {
      setLoading(false)
    }
  }

  // تشغيل LM Studio
  const launchLMStudio = async () => {
    try {
      const success = await window.electronAPI.launchLMStudio()
      if (success) {
        setTimeout(updateStatus, 3000) // انتظار 3 ثوان ثم تحديث الحالة
      }
    } catch (error) {
      console.error('خطأ في تشغيل LM Studio:', error)
    }
  }

  // حفظ الإعدادات
  const saveSettings = async () => {
    try {
      await window.electronAPI.updateAISettings({
        openRouterApiKey: openRouterKey,
        preferredProvider: preferredProvider,
        fallbackToOpenRouter: fallbackEnabled
      })
      
      // تحديث الحالة بعد الحفظ
      await updateStatus()
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error)
    }
  }

  useEffect(() => {
    updateStatus()
  }, [])

  const getStatusIcon = (available: boolean) => {
    if (available) {
      return <CheckCircle className="w-5 h-5 text-green-500" />
    } else {
      return <XCircle className="w-5 h-5 text-red-500" />
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Settings className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              إعدادات الذكاء الاصطناعي
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* حالة الخدمات */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                حالة الخدمات
              </h3>
              <button
                onClick={updateStatus}
                disabled={loading}
                className="flex items-center gap-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>

            {serviceStatus && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* LM Studio */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                  <div className="flex items-center gap-3 mb-2">
                    <Home className="w-5 h-5 text-blue-500" />
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      LM Studio (محلي)
                    </h4>
                    {getStatusIcon(serviceStatus.lmStudio.available)}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    {serviceStatus.lmStudio.status}
                  </p>
                  {!serviceStatus.lmStudio.available && (
                    <button
                      onClick={launchLMStudio}
                      className="flex items-center gap-2 px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
                    >
                      <Play className="w-4 h-4" />
                      تشغيل LM Studio
                    </button>
                  )}
                </div>

                {/* OpenRouter */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                  <div className="flex items-center gap-3 mb-2">
                    <Globe className="w-5 h-5 text-green-500" />
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      OpenRouter (سحابي)
                    </h4>
                    {getStatusIcon(serviceStatus.openRouter.available)}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {serviceStatus.openRouter.status}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* إعدادات OpenRouter */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              إعدادات OpenRouter
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  مفتاح API
                </label>
                <input
                  type="password"
                  value={openRouterKey}
                  onChange={(e) => setOpenRouterKey(e.target.value)}
                  placeholder="أدخل مفتاح OpenRouter API"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  يمكنك الحصول على مفتاح مجاني من openrouter.ai
                </p>
              </div>
            </div>
          </div>

          {/* إعدادات عامة */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              الإعدادات العامة
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الخدمة المفضلة
                </label>
                <select
                  value={preferredProvider}
                  onChange={(e) => setPreferredProvider(e.target.value as 'lmstudio' | 'openrouter')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="lmstudio">🏠 LM Studio (محلي)</option>
                  <option value="openrouter">🌐 OpenRouter (سحابي)</option>
                </select>
              </div>

              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="fallback"
                  checked={fallbackEnabled}
                  onChange={(e) => setFallbackEnabled(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded"
                />
                <label htmlFor="fallback" className="text-sm text-gray-700 dark:text-gray-300">
                  التبديل التلقائي لـ OpenRouter عند عدم توفر LM Studio
                </label>
              </div>
            </div>
          </div>

          {/* النماذج المتاحة */}
          {models && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                النماذج المتاحة
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    🏠 النماذج المحلية ({models.local.length})
                  </h4>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {models.local.map((model, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 p-2 rounded">
                        {model.name}
                      </div>
                    ))}
                    {models.local.length === 0 && (
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        لا توجد نماذج محلية متاحة
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    🌐 النماذج السحابية ({models.online.length})
                  </h4>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {models.online.slice(0, 5).map((model, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 p-2 rounded">
                        {model.name}
                      </div>
                    ))}
                    {models.online.length > 5 && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        و {models.online.length - 5} نموذج آخر...
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* أزرار الحفظ */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              إلغاء
            </button>
            <button
              onClick={saveSettings}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              حفظ الإعدادات
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
