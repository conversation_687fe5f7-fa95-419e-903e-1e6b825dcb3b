@echo off
chcp 65001 >nul
title إصلاح شامل للمشروع

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔧 إصلاح شامل للمشروع                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 بدء الإصلاح الشامل...
echo.

echo 🧹 الخطوة 1: تنظيف npm cache...
npm cache clean --force
echo ✅ تم تنظيف cache

echo.
echo 🗑️ الخطوة 2: حذف node_modules التالف...
if exist "node_modules" (
    echo 🔄 حذف node_modules...
    rmdir /s /q node_modules
    echo ✅ تم حذف node_modules
) else (
    echo ⚠️ node_modules غير موجود
)

echo.
echo 🗑️ الخطوة 3: حذف package-lock.json...
if exist "package-lock.json" (
    del package-lock.json
    echo ✅ تم حذف package-lock.json
) else (
    echo ⚠️ package-lock.json غير موجود
)

echo.
echo 📦 الخطوة 4: تثبيت جديد وكامل...
echo 🔄 هذا قد يستغرق بضع دقائق...
echo.

npm install --no-optional --no-audit --no-fund

echo.
if errorlevel 1 (
    echo ❌ فشل في التثبيت!
    echo 💡 جرب تشغيل الأمر يدوياً: npm install
    pause
    exit /b 1
) else (
    echo ✅ تم التثبيت بنجاح!
)

echo.
echo 🔍 الخطوة 5: فحص التثبيت...
npm list vite react react-dom --depth=0

echo.
echo 🚀 الخطوة 6: تشغيل المشروع...
echo.

npm run dev

echo.
echo 📝 انتهى الإصلاح
pause
