.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.app-container {
  display: flex;
  height: 100vh;
  position: relative;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-radius: 12px 0 0 12px;
  margin: 8px 8px 8px 0;
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }
  
  .main-content {
    border-radius: 0;
    margin: 0;
  }
}

/* تأثيرات الانتقال */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-in;
}

.slide-enter {
  transform: translateX(-100%);
}

.slide-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-out;
}

.slide-exit {
  transform: translateX(0);
}

.slide-exit-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-out;
}
